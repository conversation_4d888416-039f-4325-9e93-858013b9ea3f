# 🎀 NovelAI插件衣服功能使用说明

## 📊 功能概述

NovelAI插件现已集成了**430套**精心收集的衣服搭配标签，来源于hayde0096的优质数据库。每套衣服都是完整的、经过验证的搭配组合。

**✅ 数据完整性**: 所有430套衣服已成功加载，无格式错误！

## 🎯 基本使用方法

### 1. 随机衣服
```bash
/画图 美少女 随机衣服          # 从所有430套中随机选择
/画图 美少女 随机衣服 职业装   # 从职业装分类中随机选择
/画图 美少女 随机衣服 女仆装   # 从女仆装分类中随机选择
```

### 2. 指定编号
```bash
/画图 美少女 001              # 使用001号衣服（职业装）
/画图 美少女 052              # 使用052号衣服（女仆装）
/画图 美少女 125              # 使用125号衣服（战斗装）
```

### 3. 图生图模式
```bash
/图生图 随机衣服              # 图生图 + 随机衣服
/图生图 001                   # 图生图 + 指定编号衣服
```

## 📋 衣服分类统计

| 分类 | 数量 | 编号范围 | 说明 |
|------|------|----------|------|
| 职业装 | 51套 | 001-051 | 西装、正装、职场装 |
| 女仆装 | 52套 | 052-103 | 经典女仆装、变种女仆装 |
| 学生装 | 21套 | 104-124 | 校服、学生制服 |
| 战斗装 | 99套 | 125-223 | 盔甲、战斗服、军装 |
| 礼服装 | 8套 | 224-231 | 晚礼服、正式礼服 |
| 休闲装 | 3套 | 232-234 | 日常休闲服装 |
| 传统装 | 9套 | 235-243 | 和服、传统服饰 |
| 角色扮演 | 8套 | 244-251 | Cosplay、特殊角色装 |
| 内衣装 | 61套 | 252-312 | 内衣、泳装、比基尼 |
| 其他装 | 118套 | 313-430 | 特殊服装、创意搭配 |

**总计**: 430套完整搭配，覆盖所有常见服装类型

## 🔍 管理命令

### 查看分类信息
```bash
/衣服分类                     # 显示所有分类概览
/衣服分类 职业装              # 显示职业装详细列表
/衣服分类 女仆装              # 显示女仆装详细列表
```

### 搜索功能
```bash
/衣服搜索 黑色                # 搜索包含"黑色"的衣服
/衣服搜索 maid                # 搜索包含"maid"的衣服
/衣服搜索 suit                # 搜索包含"suit"的衣服
```

### 统计信息
```bash
/衣服统计                     # 显示数据库统计信息
/衣服重载                     # 重新加载数据库（管理员）
```

## 💡 使用技巧

### 1. 组合使用
```bash
/画图 1girl, beautiful face, 随机衣服
/画图 anime girl, detailed, 001
```

### 2. 参数组合
```bash
/画图 -s0.6 -n0.2 美少女 随机衣服 职业装
/图生图 -s0.8 随机衣服 女仆装
```

### 3. 预设组合
```bash
/画图 可爱预设 随机衣服
/画图 性感预设 001
```

## 🎨 示例效果

### 职业装示例（001号）
```
black beret, black lolita-style dress, frills, red bow tie, 
black suit coat, black pantyhose, black shoes, holding a cane, 
carrying large black suitcase
```

### 女仆装示例（052号）
```
ascot, detached sleeves, frilled sleeves, frills, white dress, 
white gloves, white thighhighs, high heels
```

### 战斗装示例（125号）
```
armor, sword, boots, gloves, belt, tight clothes, thigh strap, 
cropped jacket, bodysuit, gauntlets
```

## ⚠️ 注意事项

1. **编号范围**: 有效编号为001-430
2. **自动替换**: 系统会自动将"随机衣服"和数字编号替换为对应的标签
3. **分类随机**: 指定分类的随机选择只在该分类范围内
4. **标签完整**: 每个编号对应一套完整的搭配，不建议拆分使用
5. **更新机制**: 数据库支持热重载，管理员可使用`/衣服重载`更新

## 🚀 高级功能

### 自动处理流程
1. **提示词输入** → 检测"随机衣服"或数字编号
2. **标签替换** → 替换为完整的衣服标签
3. **自动翻译** → 如果启用，进行中英文翻译
4. **图片生成** → 调用NovelAI API生成图片

### 日志记录
- 系统会记录每次衣服标签的使用情况
- 管理员可通过日志查看使用统计
- 支持使用频率分析和优化建议

## 📞 技术支持

如果遇到问题：
1. 检查数据库文件是否存在
2. 使用`/衣服重载`重新加载数据
3. 查看插件日志获取详细错误信息
4. 确认编号在有效范围内（001-430）

---

**数据来源**: [hayde0096/Kisegaeningyou](https://github.com/hayde0096/Kisegaeningyou)  
**更新时间**: 2024-01-01  
**版本**: v1.0.0
