# 百度翻译API版本说明

## 📋 **当前使用版本**

本插件使用的是 **百度翻译2.0版本API**，具体接口如下：

### **通用版翻译API**
- **接口地址**: `https://aip.baidubce.com/rpc/2.0/mt/texttrans/v1`
- **认证方式**: access_token
- **特点**: 功能丰富，支持更多语言对，QPS限制相对宽松

### **词典版翻译API**
- **接口地址**: `https://aip.baidubce.com/rpc/2.0/mt/texttrans-with-dict/v1`
- **认证方式**: access_token
- **特点**: 包含词典信息，翻译更准确，但QPS限制更严格

## 🔧 **配置方法**

### **1. 获取API密钥**
1. 访问 [百度智能云控制台](https://ai.baidu.com/)
2. 登录并进入"产品服务" → "机器翻译"
3. 创建应用，获取 **APP ID** 和 **API Key**

### **2. 插件配置**
在后台管理界面中配置：
- **百度翻译APP ID**: 填入获取的APP ID
- **百度翻译密钥**: 填入获取的API Key (Secret Key)
- **百度翻译API类型**: 选择 `general`(通用版) 或 `dict`(词典版)

### **⚠️ 重要提醒**
- **翻译API** 和 **审核API** 使用不同的密钥配置
- 翻译功能需要配置 "百度翻译APP ID" 和 "百度翻译密钥"
- 审核功能需要配置 "百度审核API Key" 和 "百度审核密钥"
- 两个功能可以独立配置和使用

## ⚠️ **重要说明**

### **版本差异**
- **1.0版本** (旧版): 使用 `fanyi-api.baidu.com`，APPID+密钥直接签名
- **2.0版本** (当前): 使用 `aip.baidubce.com`，APPID+密钥获取access_token

### **认证流程**
1. 使用APP ID和API Key获取access_token
2. 使用access_token调用翻译接口
3. access_token会自动缓存和刷新

### **QPS限制**
- **通用版**: QPS限制相对宽松，推荐日常使用
- **词典版**: QPS限制较严格，适合高精度翻译需求

## 🎯 **推荐配置**

### **新用户推荐**
- **API类型**: `general` (通用版)
- **原因**: QPS限制宽松，功能完整，适合大多数场景

### **高精度需求**
- **API类型**: `dict` (词典版)
- **原因**: 包含词典信息，翻译更准确
- **注意**: QPS限制较严，可能需要付费版本

## 🔍 **故障排除**

### **常见错误**

#### **错误码18: QPS超限**
- **原因**: 请求频率过高
- **解决**: 切换到通用版API，或升级到付费版本

#### **错误码54004: 余额不足**
- **原因**: API配额用完
- **解决**: 充值或等待配额重置

#### **认证失败**
- **原因**: APP ID或API Key错误
- **解决**: 检查配置，确保使用2.0版本的密钥

## 📚 **参考文档**

- [百度翻译API官方文档](https://ai.baidu.com/ai-doc/MT/4kqryjku9)
- [百度智能云控制台](https://console.bce.baidu.com/)
- [API错误码说明](https://ai.baidu.com/ai-doc/MT/Ykqryjkv0)

---

**文档版本**: v1.0  
**API版本**: 百度翻译2.0  
**更新时间**: 2024-01-01
