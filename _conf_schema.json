{"api_token": {"type": "string", "title": "NovelAI API Token", "default": "", "description": "NovelAI API访问令牌，必填项。在NovelAI官网账户设置中生成"}, "default_model": {"type": "string", "title": "默认模型", "default": "nai-diffusion-4-5-full", "description": "默认模型。推荐 nai-diffusion-4-5-full"}, "default_resolution": {"type": "string", "title": "默认分辨率", "default": "832x1216", "description": "默认分辨率。832x1216竖屏、1216x832横屏、1024x1024方形"}, "default_steps": {"type": "int", "title": "默认步数", "default": 28, "description": "图片生成的默认步数，范围1-50。步数越高质量越好但耗时更长"}, "default_scale": {"type": "int", "title": "默认权重", "default": 7, "description": "提示词引导权重，范围1-20。权重越高越严格按照提示词生成"}, "default_sampler": {"type": "string", "default": "k_euler", "description": "默认采样器"}, "img2img_strength": {"type": "float", "title": "图生图强度", "description": "图生图强度0.1-0.9。值越低保留原图越多", "default": 0.5, "minimum": 0.1, "maximum": 0.9, "multipleOf": 0.1}, "img2img_noise": {"type": "float", "title": "图生图噪声", "description": "图生图噪声0.0-0.4。值越高细节越多", "default": 0.2, "minimum": 0.0, "maximum": 0.4, "multipleOf": 0.1}, "enable_nsfw": {"type": "bool", "title": "启用NSFW内容", "default": false, "description": "是否允许生成NSFW（成人）内容"}, "max_daily_usage": {"type": "int", "title": "每日使用限制", "default": 50, "description": "每个用户每日最大使用次数，0表示无限制"}, "cooldown_seconds": {"type": "int", "title": "冷却时间", "default": 10, "description": "用户使用间隔冷却时间（秒）"}, "whitelist_groups": {"type": "string", "title": "白名单群组", "default": "", "description": "允许使用插件的群组ID，用逗号分隔。留空表示所有群组都可使用"}, "image_proxy": {"type": "string", "title": "图片代理", "default": "", "description": "图片下载代理地址，格式：http://proxy:port"}, "api_proxy": {"type": "string", "title": "API代理", "default": "", "description": "NovelAI API代理地址，格式：http://proxy:port"}, "enable_style_preset": {"type": "bool", "default": false, "description": "是否启用预设画风"}, "selected_style_preset": {"type": "string", "default": "", "description": "当前选中的预设画风"}, "enable_negative_prompt": {"type": "bool", "default": false, "description": "是否启用负面提示词"}, "selected_negative_prompt": {"type": "string", "default": "", "description": "当前选中的负面提示词预设"}, "presets": {"type": "string", "default": "{}", "description": "提示词预设(JSON格式)"}, "style_presets": {"type": "string", "default": "{}", "description": "画风预设(JSON格式)"}, "negative_prompts": {"type": "string", "default": "{}", "description": "负面提示词预设(JSON格式)"}, "admin_only_commands": {"type": "string", "default": "", "description": "仅管理员可用的命令(逗号分隔)"}, "enable_auto_save": {"type": "bool", "title": "启用自动保存", "default": false, "description": "是否自动保存生成的图片到本地"}, "save_path": {"type": "string", "title": "保存路径", "default": "novelai_images", "description": "图片自动保存的文件夹路径"}, "show_params": {"type": "bool", "title": "显示生成参数", "default": true, "description": "在生成结果中显示详细的生成参数信息"}, "enable_fake_forward_presets": {"type": "bool", "title": "启用预设转发", "default": true, "description": "预设列表以转发消息形式显示，提供更好的视觉效果"}, "preset_categories": {"type": "string", "default": "{}", "description": "预设分类(JSON格式)"}, "baidu_translate_appid": {"type": "string", "title": "百度翻译APP ID", "default": "", "description": "百度翻译2.0 API的APP ID。在 ai.baidu.com 创建应用获取"}, "baidu_translate_secret": {"type": "string", "title": "百度翻译密钥", "default": "", "description": "百度翻译2.0 API的密钥，与APP ID配对使用"}, "enable_auto_translate": {"type": "bool", "title": "启用自动翻译", "default": false, "description": "自动翻译中文提示词为英文。需先配置百度翻译API"}, "baidu_translate_api_type": {"type": "string", "title": "百度翻译API类型", "default": "general", "enum": ["general", "dict"], "enumNames": ["通用版", "词典版"], "description": "填写 general 或 dict。通用版功能丰富，词典版更准确"}, "baidu_audit_api_key": {"type": "string", "title": "百度审核API Key", "default": "", "description": "百度审核API Key。在 ai.baidu.com 创建内容审核应用获取"}, "baidu_audit_secret_key": {"type": "string", "title": "百度审核Secret Key", "default": "", "description": "百度内容审核API的Secret Key，与API Key配对使用"}, "enable_image_audit": {"type": "bool", "title": "启用图片审核", "default": false, "description": "自动检测图片内容合规性。不合规图片会被拦截"}, "audit_failed_save_path": {"type": "string", "title": "审核失败图片保存路径", "default": "novelai_audit_failed", "description": "审核失败图片的保存路径"}, "audit_conclusion_type": {"type": "int", "title": "审核严格程度", "default": 2, "description": "审核严格程度：1最严格，2中等，3最宽松"}, "audit_enable_politics": {"type": "bool", "title": "启用政治敏感审核", "default": true, "description": "是否检测政治敏感内容"}, "audit_enable_porn": {"type": "bool", "title": "启用色情内容审核", "default": true, "description": "是否检测色情内容"}, "audit_enable_terror": {"type": "bool", "title": "启用暴恐内容审核", "default": true, "description": "是否检测暴力恐怖内容"}, "audit_enable_disgust": {"type": "bool", "title": "启用恶心内容审核", "default": true, "description": "是否检测恶心内容"}}