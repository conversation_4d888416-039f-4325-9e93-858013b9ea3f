# NovelAI插件后台配置说明

## 🎛️ 管理面板配置

NovelAI插件现在支持在AstrBot管理面板中进行可视化配置，无需手动编辑配置文件。

### 📋 配置项说明

#### **基础配置**
- **NovelAI API Token** - NovelAI API访问令牌（必填）
- **默认模型** - 选择默认使用的NovelAI模型
- **默认分辨率** - 图片生成的默认分辨率
- **默认步数** - 图片生成的默认步数（1-50）
- **默认权重** - 提示词权重（1-20）

#### **翻译功能配置**
- **百度翻译APP ID** - 百度翻译API的APP ID（可选）
- **百度翻译密钥** - 百度翻译API的密钥（可选）
- **启用自动翻译** - 画图时自动翻译中文提示词为英文

#### **图片审核配置**
- **百度审核API Key** - 百度内容审核API的API Key（可选）
- **百度审核Secret Key** - 百度内容审核API的Secret Key（可选）
- **启用图片审核** - 自动检测生成的图片内容是否合规
- **审核失败图片保存路径** - 审核不通过的图片保存位置
- **审核严格程度** - 1=严格，2=中等，3=宽松
- **启用政治敏感审核** - 是否检测政治敏感内容
- **启用色情内容审核** - 是否检测色情内容
- **启用暴恐内容审核** - 是否检测暴力恐怖内容
- **启用恶心内容审核** - 是否检测恶心内容

#### **功能开关**
- **启用NSFW内容** - 是否允许生成NSFW内容
- **启用自动保存** - 自动保存生成的图片
- **显示生成参数** - 在结果中显示详细的生成参数

#### **使用限制**
- **每日使用限制** - 每个用户每日最大使用次数（1-1000）

#### **代理配置**
- **图片代理** - 图片下载代理地址（可选）
- **API代理** - NovelAI API代理地址（可选）

### 🔧 配置步骤

1. **登录AstrBot管理面板**
   - 访问AstrBot的Web管理界面（通常是 http://localhost:6185）
   - 使用管理员账号登录

2. **找到NovelAI插件配置**
   - 在左侧菜单中找到"插件管理"
   - 找到"astrbot_plugin_novelai"插件
   - 点击"配置"按钮

3. **填写配置信息**
   - **必填项**：NovelAI API Token
   - **翻译功能**：百度翻译APP ID和密钥（可选）
   - **其他选项**：根据需要配置

4. **保存配置**
   - 点击"保存"按钮
   - 配置会立即生效，无需重启AstrBot

### 📱 **配置界面预览**

在管理面板中，您会看到以下配置分组：

#### **基础配置**
- NovelAI API Token ⭐（必填）
- 默认模型
- 默认分辨率
- 默认步数
- 默认权重

#### **翻译功能**
- 百度翻译APP ID
- 百度翻译密钥
- 启用自动翻译

#### **图片审核**
- 百度审核API Key
- 百度审核Secret Key
- 启用图片审核
- 审核失败图片保存路径
- 审核严格程度
- 启用政治敏感审核
- 启用色情内容审核
- 启用暴恐内容审核
- 启用恶心内容审核

#### **功能开关**
- 启用NSFW内容
- 启用自动保存
- 显示生成参数
- 启用预设转发

#### **使用限制**
- 每日使用限制
- 冷却时间
- 白名单群组

#### **代理配置**
- 图片代理
- API代理

### 🌐 百度翻译API配置

如果需要使用翻译功能，请按以下步骤获取API：

1. **注册百度翻译开放平台**
   - 访问：https://fanyi-api.baidu.com/
   - 注册账号并登录

2. **创建应用**
   - 在控制台创建新的翻译应用
   - 获取APP ID和密钥

3. **在管理面板中配置**
   - 填写"百度翻译APP ID"
   - 填写"百度翻译密钥"
   - 根据需要开启"启用自动翻译"

### 🔍 百度图片审核API配置

如果需要使用图片审核功能，请按以下步骤获取API：

1. **注册百度智能云**
   - 访问：https://ai.baidu.com/
   - 注册账号并登录

2. **创建内容审核应用**
   - 在控制台找到"内容审核"服务
   - 创建新的应用
   - 获取API Key和Secret Key

3. **在管理面板中配置**
   - 填写"百度审核API Key"
   - 填写"百度审核Secret Key"
   - 开启"启用图片审核"
   - 设置"审核失败图片保存路径"（可选）
   - 配置"审核严格程度"（1-3，推荐2）
   - 选择需要审核的内容类型

### 📊 **审核参数说明**

#### **审核严格程度**
- **1 - 严格模式**：只有完全合规的图片才能通过
- **2 - 中等模式**：合规和疑似违规都会被拦截（推荐）
- **3 - 宽松模式**：只有明确违规的图片才会被拦截

#### **审核内容类型**
- **政治敏感**：检测政治敏感内容
- **色情内容**：检测色情、性暗示内容
- **暴恐内容**：检测暴力、恐怖内容
- **恶心内容**：检测令人恶心的内容

#### **推荐配置**
- 审核严格程度：2（中等模式）
- 全部内容类型：启用（确保全面保护）

### ✅ 配置验证

插件会自动验证配置的有效性：

- **API Token验证** - 检查是否为空
- **翻译配置验证** - 检查APP ID和密钥是否成对配置
- **数值范围验证** - 检查步数、权重等是否在有效范围内

### 🔄 配置更新

- 配置更改后会立即生效
- 无需重启AstrBot或重载插件
- 系统会自动记录配置更改日志

### 💡 使用建议

1. **首次配置**：先配置基础的API Token和模型
2. **翻译功能**：如需中文支持，建议配置百度翻译API
3. **使用限制**：根据实际需求调整每日使用限制
4. **代理配置**：如网络环境需要，可配置代理地址

### 🆘 常见问题

**Q: 配置保存后不生效？**
A: 检查配置验证是否通过，查看错误提示信息

**Q: 翻译功能不工作？**
A: 确保百度翻译APP ID和密钥都已正确配置

**Q: 如何重置配置？**
A: 在管理面板中清空相关配置项，或使用默认值

### 📞 技术支持

如遇到配置问题，请检查：
1. AstrBot日志中的错误信息
2. 配置项是否符合验证规则
3. 网络连接是否正常
