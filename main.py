import asyncio
import base64
import gzip
import json
import time
import random
import os
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Set, Tuple
import io
import zipfile
import re

import httpx
import numpy as np
from PIL import Image as PILImage

# 导入AstrBot相关模块
from astrbot.api.event import filter
import astrbot.api.message_components as Comp
from astrbot.api.star import Context, Star, register
from astrbot.api import logger, AstrBotConfig

# 导入衣服管理器
from .clothing_manager import ClothingManager

# 自定义异常类
class NovelAIError(Exception):
    """NovelAI插件基础异常类"""
    pass

class ImageProcessingError(NovelAIError):
    """图片处理异常"""
    pass

class MetadataExtractionError(NovelAIError):
    """元数据提取异常"""
    pass

class APIError(NovelAIError):
    """API调用异常"""
    pass

class ValidationError(NovelAIError):
    """验证异常"""
    pass

# 性能监控装饰器
def performance_monitor(func_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                if execution_time > 1.0:  # 超过1秒记录警告
                    logger.warning(f"{func_name or func.__name__} 执行时间较长: {execution_time:.2f}s")
                else:
                    logger.debug(f"{func_name or func.__name__} 执行时间: {execution_time:.2f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{func_name or func.__name__} 执行失败 (耗时: {execution_time:.2f}s): {e}")
                raise
        return wrapper
    return decorator

# 常量定义
class Constants:
    # API URLs
    NOVELAI_API_BASE = "https://image.novelai.net"
    BAIDU_TRANSLATE_GENERAL_API = "https://aip.baidubce.com/rpc/2.0/mt/texttrans/v1"
    BAIDU_TRANSLATE_DICT_API = "https://aip.baidubce.com/rpc/2.0/mt/texttrans-with-dict/v1"
    BAIDU_OAUTH_API = "https://aip.baidubce.com/oauth/2.0/token"
    BAIDU_AUDIT_API = "https://aip.baidubce.com/rest/2.0/solution/v1/img_censor/v2/user_defined"

    # 默认配置
    DEFAULT_MODEL = "nai-diffusion-4-5-full"
    DEFAULT_RESOLUTION = "832x1216"
    DEFAULT_STEPS = 28
    DEFAULT_SCALE = 7
    DEFAULT_SAMPLER = "k_euler"
    DEFAULT_COOLDOWN = 10
    DEFAULT_MAX_DAILY_USAGE = 50

    # 审核相关
    AUDIT_CONCLUSION_STRICT = 1
    AUDIT_CONCLUSION_MODERATE = 2
    AUDIT_CONCLUSION_LOOSE = 3

    # 文件路径
    DEFAULT_SAVE_PATH = "novelai_images"
    DEFAULT_AUDIT_FAILED_PATH = "novelai_audit_failed"

    # 性能限制
    MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10MB 最大图片大小
    MAX_IMAGE_DIMENSION = 4096  # 最大图片尺寸
    LARGE_IMAGE_THRESHOLD = 5 * 1024 * 1024  # 5MB 大图片阈值

    # 预设分类
    DEFAULT_CATEGORIES = {
        "人物": {"description": "角色、人物相关的预设", "presets": []},
        "衣服": {"description": "服装、配饰相关的预设", "presets": []},
        "动作": {"description": "姿势、动作相关的预设", "presets": []},
        "场景": {"description": "背景、环境相关的预设", "presets": []},
        "风格": {"description": "画风、风格相关的预设", "presets": []},
        "其他": {"description": "其他类型的预设", "presets": []}
    }

class CacheManager:
    """缓存管理器"""

    def __init__(self):
        self._cache = {}
        self._cache_timestamps = {}
        self._default_ttl = 300  # 5分钟默认TTL

    def get(self, key: str, default=None):
        """获取缓存值"""
        if key in self._cache:
            # 检查是否过期
            if time.time() - self._cache_timestamps[key] < self._default_ttl:
                return self._cache[key]
            else:
                # 过期，删除缓存
                self.delete(key)
        return default

    def set(self, key: str, value: Any, ttl: int = None):
        """设置缓存值"""
        self._cache[key] = value
        self._cache_timestamps[key] = time.time()
        if ttl:
            # 设置自定义TTL（暂时简化实现）
            pass

    def delete(self, key: str):
        """删除缓存"""
        self._cache.pop(key, None)
        self._cache_timestamps.pop(key, None)

    def clear(self):
        """清空所有缓存"""
        self._cache.clear()
        self._cache_timestamps.clear()

class HttpClientManager:
    """HTTP客户端管理器"""

    def __init__(self):
        self._clients = {}
        self._default_timeout = 30.0

    def get_client(self, client_type: str = "default", timeout: float = None) -> httpx.AsyncClient:
        """获取HTTP客户端"""
        if client_type not in self._clients:
            timeout_value = timeout or self._default_timeout
            self._clients[client_type] = httpx.AsyncClient(
                timeout=timeout_value,
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
            )
        return self._clients[client_type]

    async def close_all(self):
        """关闭所有客户端"""
        for client in self._clients.values():
            await client.aclose()
        self._clients.clear()

class ConfigManager:
    """配置管理器"""

    def __init__(self, config: AstrBotConfig):
        self.config = config
        self._config_cache = {}

    def get_with_default(self, key: str, default_value: Any, value_type: type = None) -> Any:
        """获取配置值，带默认值和类型验证"""
        if key in self._config_cache:
            return self._config_cache[key]

        value = self.config.get(key, default_value)
        if value_type:
            value = Utils.validate_config_value(value, value_type, default_value)

        self._config_cache[key] = value
        return value

    def update_cache(self, key: str, value: Any):
        """更新缓存"""
        self._config_cache[key] = value

    def clear_cache(self):
        """清空配置缓存"""
        self._config_cache.clear()

    def get_api_config(self) -> Dict[str, Any]:
        """获取API相关配置"""
        return {
            'api_token': self.get_with_default('api_token', ''),
            'api_proxy': self.get_with_default('api_proxy', ''),
            'image_proxy': self.get_with_default('image_proxy', ''),
        }

    def get_baidu_translate_config(self) -> Dict[str, Any]:
        """获取百度翻译配置"""
        return {
            'appid': self.get_with_default('baidu_translate_appid', ''),
            'secret': self.get_with_default('baidu_translate_secret', ''),
            'enable_auto': self.get_with_default('enable_auto_translate', False, bool),
            'api_type': self.get_with_default('baidu_translate_api_type', 'general', str),
        }

    def get_baidu_audit_config(self) -> Dict[str, Any]:
        """获取百度审核配置"""
        return {
            'api_key': self.get_with_default('baidu_audit_api_key', ''),
            'secret_key': self.get_with_default('baidu_audit_secret_key', ''),
            'enable': self.get_with_default('enable_image_audit', False, bool),
            'save_path': self.get_with_default('audit_failed_save_path', Constants.DEFAULT_AUDIT_FAILED_PATH),
            'conclusion_type': self.get_with_default('audit_conclusion_type', Constants.AUDIT_CONCLUSION_MODERATE, int),
            'enable_politics': self.get_with_default('audit_enable_politics', True, bool),
            'enable_porn': self.get_with_default('audit_enable_porn', True, bool),
            'enable_terror': self.get_with_default('audit_enable_terror', True, bool),
            'enable_disgust': self.get_with_default('audit_enable_disgust', True, bool),
        }

class Utils:
    """工具方法类"""

    @staticmethod
    def safe_json_loads(json_str: str, default_value: Any = None) -> Any:
        """安全的JSON解析"""
        try:
            return json.loads(json_str)
        except (json.JSONDecodeError, TypeError) as e:
            logger.warning(f"JSON解析失败: {e}")
            return default_value or {}

    @staticmethod
    def safe_json_dumps(obj: Any, default_value: str = "{}") -> str:
        """安全的JSON序列化"""
        try:
            return json.dumps(obj, ensure_ascii=False)
        except (TypeError, ValueError) as e:
            logger.warning(f"JSON序列化失败: {e}")
            return default_value

    @staticmethod
    def validate_config_value(value: Any, value_type: type, default_value: Any) -> Any:
        """验证配置值类型"""
        try:
            if value_type == bool:
                return bool(value)
            elif value_type == int:
                return int(value)
            elif value_type == float:
                return float(value)
            else:
                return value
        except (ValueError, TypeError):
            return default_value

    @staticmethod
    def create_directory_if_not_exists(path: str) -> bool:
        """创建目录（如果不存在）"""
        try:
            os.makedirs(path, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"创建目录失败 {path}: {e}")
            return False

    @staticmethod
    def generate_cache_key(*args) -> str:
        """生成缓存键"""
        return hashlib.md5(str(args).encode()).hexdigest()

    @staticmethod
    def is_valid_image_data(data: bytes) -> bool:
        """验证图片数据是否有效"""
        try:
            img = PILImage.open(io.BytesIO(data))
            img.verify()
            return True
        except Exception:
            return False

    @staticmethod
    def async_retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
        """异步重试装饰器"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                last_exception = None
                for attempt in range(max_attempts):
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        if attempt < max_attempts - 1:
                            wait_time = delay * (backoff ** attempt)
                            logger.warning(f"{func.__name__} 第{attempt + 1}次尝试失败: {e}, {wait_time:.1f}秒后重试")
                            await asyncio.sleep(wait_time)
                        else:
                            logger.error(f"{func.__name__} 所有重试都失败了: {e}")

                # 如果所有重试都失败，抛出最后一个异常
                raise last_exception
            return wrapper
        return decorator

    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f}{size_names[i]}"

class ApiResponseHandler:
    """API响应处理器"""

    @staticmethod
    def handle_http_response(response: httpx.Response, api_name: str = "API") -> Dict[str, Any]:
        """统一处理HTTP响应"""
        result = {
            'success': False,
            'data': None,
            'error': None,
            'status_code': response.status_code
        }

        try:
            if response.status_code == 200:
                # 尝试解析JSON
                try:
                    result['data'] = response.json()
                    result['success'] = True
                except Exception:
                    # 如果不是JSON，返回原始内容
                    result['data'] = response.content
                    result['success'] = True
            else:
                result['error'] = f"{api_name}请求失败: HTTP {response.status_code}"
                try:
                    error_data = response.json()
                    if 'error_msg' in error_data:
                        result['error'] += f" - {error_data['error_msg']}"
                except Exception:
                    result['error'] += f" - {response.text[:200]}"

        except Exception as e:
            result['error'] = f"{api_name}响应处理异常: {e}"

        return result

    @staticmethod
    def extract_error_message(response_data: Dict[str, Any], default_msg: str = "未知错误") -> str:
        """从响应数据中提取错误信息"""
        if not response_data.get('success', False):
            return response_data.get('error', default_msg)

        # 检查业务层面的错误
        data = response_data.get('data', {})
        if isinstance(data, dict):
            if 'error_code' in data:
                error_code = data.get('error_code')
                error_msg = data.get('error_msg', default_msg)
                return f"错误码{error_code}: {error_msg}"

        return default_msg

# 添加错误类型定义
class NovelAIError:
    """NovelAI错误类型定义"""
    
    class NetworkError(Exception):
        """网络连接错误"""
        def __init__(self, message="网络连接错误", original_error=None):
            self.message = message
            self.original_error = original_error
            super().__init__(self.message)
            
        def __str__(self):
            if self.original_error:
                return f"{self.message}: {self.original_error}"
            return self.message
            
    class APIError(Exception):
        """API调用错误"""
        def __init__(self, status_code=None, response_text=None, message=None):
            self.status_code = status_code
            self.response_text = response_text
            self.message = message or f"API错误 (状态码: {status_code})"
            super().__init__(self.message)
            
        def __str__(self):
            if self.response_text:
                # 尝试解析JSON响应
                try:
                    error_json = json.loads(self.response_text)
                    if 'error' in error_json:
                        return f"API错误 ({self.status_code}): {error_json['error']}"
                    if 'message' in error_json:
                        return f"API错误 ({self.status_code}): {error_json['message']}"
                except (json.JSONDecodeError, KeyError):
                    pass
                # 如果响应文本太长，截断它
                if len(self.response_text) > 100:
                    return f"API错误 ({self.status_code}): {self.response_text[:100]}..."
                return f"API错误 ({self.status_code}): {self.response_text}"
            return self.message
            
    class AuthenticationError(APIError):
        """认证错误"""
        def __init__(self, status_code=401, response_text=None):
            super().__init__(status_code, response_text, "API认证失败，请检查Token是否正确")
            
    class QuotaExceededError(APIError):
        """配额超限错误"""
        def __init__(self, status_code=429, response_text=None):
            super().__init__(status_code, response_text, "API使用次数超限，请稍后再试")
            
    class ServerError(APIError):
        """服务器错误"""
        def __init__(self, status_code=None, response_text=None):
            super().__init__(status_code, response_text, f"NovelAI服务器错误 ({status_code})")
            
    class ConfigurationError(Exception):
        """配置错误"""
        def __init__(self, message="配置错误"):
            self.message = message
            super().__init__(self.message)
            
    class ImageProcessingError(Exception):
        """图片处理错误"""
        def __init__(self, message="图片处理错误", original_error=None):
            self.message = message
            self.original_error = original_error
            super().__init__(self.message)
            
        def __str__(self):
            if self.original_error:
                return f"{self.message}: {self.original_error}"
            return self.message
            
    class ParameterError(Exception):
        """参数错误"""
        def __init__(self, message="参数错误"):
            self.message = message
            super().__init__(self.message)


class UsageManager:
    """使用量和排队管理器"""

    def __init__(self, max_daily: int = 50, cooldown: int = 10):
        self.max_daily_usage = max_daily
        self.cooldown_seconds = cooldown
        self.usage_data_file = os.path.join("data", "usage_data.json")
        self.usage_data: Dict[str, Dict[str, int]] = self._load_usage_data()
        self.last_use: Dict[str, datetime] = {}
        self.processing_request: Optional[str] = None  # 当前正在处理的请求ID
        self.queue: List[Dict[str, Any]] = []  # 排队列表，存储请求信息
        self.queue_lock = asyncio.Lock()  # 队列锁
        self.request_events: Dict[str, asyncio.Event] = {}  # 请求等待事件
        self.request_counter = 0  # 请求计数器，用于生成唯一ID
        
    def generate_request_id(self, user_id: str) -> str:
        """生成唯一的请求ID"""
        self.request_counter += 1
        return f"{user_id}_{self.request_counter}_{int(time.time() * 1000)}"
        
    def check_daily_limit(self, user_id: str) -> bool:
        """检查用户是否达到每日使用限制"""
        if self.max_daily_usage <= 0:  # 0 表示无限制
            return True
            
        today = datetime.now().strftime("%Y-%m-%d")
        user_usage = self.usage_data.get(user_id, {})
        return user_usage.get(today, 0) < self.max_daily_usage
    
    def check_cooldown(self, user_id: str) -> bool:
        """检查用户是否在冷却期内"""
        last_time = self.last_use.get(user_id)
        if not last_time:
            return True
        return datetime.now() - last_time > timedelta(seconds=self.cooldown_seconds)
    
    async def add_to_queue(self, user_id: str) -> tuple[str, bool, int, int]:
        """
        添加请求到队列
        返回: (请求ID, 是否可以立即处理, 队列位置, 预计等待时间)
        """
        request_id = self.generate_request_id(user_id)
        
        async with self.queue_lock:
            # 如果没有正在处理的请求且队列为空，可以立即处理
            if not self.processing_request and not self.queue:
                self.processing_request = request_id
                logger.info(f"请求 {request_id} (用户 {user_id}) 开始处理（无需排队）")
                return request_id, True, 0, 0
            
            # 否则加入队列
            request_info = {
                'request_id': request_id,
                'user_id': user_id,
                'timestamp': datetime.now()
            }
            self.queue.append(request_info)
            
            # 为请求创建等待事件
            self.request_events[request_id] = asyncio.Event()
            
            queue_position = len(self.queue)
            # 预计时间 = 当前处理 + 队列前面的请求数 * 8秒
            estimated_time = 8 + (queue_position - 1) * 8
            
            logger.info(f"请求 {request_id} (用户 {user_id}) 加入队列，位置: {queue_position}")
            return request_id, False, queue_position, estimated_time
    
    async def wait_for_turn(self, request_id: str):
        """等待轮到该请求"""
        if request_id in self.request_events:
            await self.request_events[request_id].wait()
            del self.request_events[request_id]
    
    async def start_next_in_queue(self) -> Optional[str]:
        """开始处理队列中的下一个请求"""
        async with self.queue_lock:
            if self.queue:
                next_request = self.queue.pop(0)
                request_id = next_request['request_id']
                user_id = next_request['user_id']
                self.processing_request = request_id
                
                # 触发请求的等待事件
                if request_id in self.request_events:
                    self.request_events[request_id].set()
                
                logger.info(f"开始处理队列中的请求: {request_id} (用户 {user_id})")
                return request_id
            
            self.processing_request = None
            return None
    
    def finish_processing(self, request_id: str):
        """完成处理请求"""
        if self.processing_request == request_id:
            self.processing_request = None
            logger.info(f"请求 {request_id} 处理完成")
    
    def _load_usage_data(self) -> Dict[str, Dict[str, int]]:
        """加载使用数据"""
        try:
            if os.path.exists(self.usage_data_file):
                with open(self.usage_data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    logger.info(f"已加载使用数据，包含 {len(data)} 个用户的记录")
                    return data
        except Exception as e:
            logger.error(f"加载使用数据失败: {e}")
        return {}

    def _save_usage_data(self):
        """保存使用数据"""
        try:
            os.makedirs("data", exist_ok=True)
            with open(self.usage_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_data, f, ensure_ascii=False, indent=2)
            logger.debug("使用数据已保存")
        except Exception as e:
            logger.error(f"保存使用数据失败: {e}")

    def record_usage(self, user_id: str):
        """记录用户使用"""
        today = datetime.now().strftime("%Y-%m-%d")
        if user_id not in self.usage_data:
            self.usage_data[user_id] = {}
        self.usage_data[user_id][today] = self.usage_data[user_id].get(today, 0) + 1
        self.last_use[user_id] = datetime.now()
        self._save_usage_data()  # 保存到文件
    
    def get_usage_info(self, user_id: str) -> Dict[str, Any]:
        """获取用户使用信息"""
        today = datetime.now().strftime("%Y-%m-%d")
        today_usage = self.usage_data.get(user_id, {}).get(today, 0)

        last_time = self.last_use.get(user_id)
        cooldown_remaining = 0
        if last_time:
            elapsed = (datetime.now() - last_time).total_seconds()
            cooldown_remaining = max(0, self.cooldown_seconds - elapsed)

        # 计算用户在队列中的请求数
        user_requests_in_queue = sum(1 for req in self.queue if req['user_id'] == user_id)

        return {
            "today_usage": today_usage,
            "max_daily": self.max_daily_usage,
            "cooldown_remaining": cooldown_remaining,
            "user_requests_in_queue": user_requests_in_queue,
            "queue_length": len(self.queue)
        }

    def get_global_statistics(self) -> Dict[str, Any]:
        """获取全局使用统计"""
        today = datetime.now().strftime("%Y-%m-%d")

        # 统计今日总使用次数
        today_total = 0
        total_users = len(self.usage_data)
        active_users_today = 0

        # 统计历史总使用次数
        total_usage = 0

        for _, user_data in self.usage_data.items():
            # 今日使用次数
            today_usage = user_data.get(today, 0)
            today_total += today_usage
            if today_usage > 0:
                active_users_today += 1

            # 历史总使用次数
            for date, count in user_data.items():
                total_usage += count

        # 获取最近7天的使用数据
        recent_days = []
        for i in range(7):
            date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
            day_total = 0
            for user_data in self.usage_data.values():
                day_total += user_data.get(date, 0)
            recent_days.append({"date": date, "count": day_total})

        return {
            "today_total": today_total,
            "total_usage": total_usage,
            "total_users": total_users,
            "active_users_today": active_users_today,
            "queue_length": len(self.queue),
            "processing_request": self.processing_request is not None,
            "recent_days": recent_days
        }

    def cleanup_old_data(self, days_to_keep: int = 30):
        """清理过期的使用数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_str = cutoff_date.strftime("%Y-%m-%d")

            cleaned_count = 0
            for user_id in list(self.usage_data.keys()):
                user_data = self.usage_data[user_id]
                # 删除过期的日期记录
                for date in list(user_data.keys()):
                    if date < cutoff_str:
                        del user_data[date]
                        cleaned_count += 1

                # 如果用户没有任何记录了，删除用户
                if not user_data:
                    del self.usage_data[user_id]

            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 条过期使用记录")
                self._save_usage_data()

        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")


@register(
    "astrbot_plugin_novelai",
    "NovelAI Plugin Author", 
    "NovelAI图像生成插件，支持文生图和图生图功能",
    "1.0.0",
    "https://github.com/yourname/astrbot_plugin_novelai",
)
class NovelAIPlugin(Star):
    """NovelAI图像生成插件"""

    # 定义配置项模式，用于管理面板显示
    CONFIG_SCHEMA = {
        "api_token": {
            "type": "string",
            "title": "NovelAI API Token",
            "description": "NovelAI API访问令牌",
            "default": "",
            "required": True
        },
        "default_model": {
            "type": "select",
            "title": "默认模型",
            "description": "默认使用的NovelAI模型",
            "options": [
                {"value": "nai-diffusion-4-5-full", "label": "NAI Diffusion 4.5 Full"},
                {"value": "nai-diffusion-4-5-furry", "label": "NAI Diffusion 4.5 Furry"},
                {"value": "nai-diffusion-3", "label": "NAI Diffusion 3"},
                {"value": "nai-diffusion-3-inpainting", "label": "NAI Diffusion 3 Inpainting"}
            ],
            "default": "nai-diffusion-4-5-full"
        },
        "default_resolution": {
            "type": "select",
            "title": "默认分辨率",
            "description": "图片生成的默认分辨率",
            "options": [
                {"value": "832x1216", "label": "832x1216 (竖屏)"},
                {"value": "1216x832", "label": "1216x832 (横屏)"},
                {"value": "1024x1024", "label": "1024x1024 (方形)"}
            ],
            "default": "832x1216"
        },
        "default_steps": {
            "type": "number",
            "title": "默认步数",
            "description": "图片生成的默认步数",
            "default": 28,
            "min": 1,
            "max": 50
        },
        "default_scale": {
            "type": "number",
            "title": "默认权重",
            "description": "提示词权重",
            "default": 7,
            "min": 1,
            "max": 20
        },
        "baidu_translate_appid": {
            "type": "string",
            "title": "百度翻译APP ID",
            "description": "百度翻译API的APP ID",
            "default": "",
            "required": False
        },
        "baidu_translate_secret": {
            "type": "string",
            "title": "百度翻译密钥",
            "description": "百度翻译API的密钥",
            "default": "",
            "required": False
        },
        "enable_auto_translate": {
            "type": "boolean",
            "title": "启用自动翻译",
            "description": "画图时自动翻译中文提示词为英文",
            "default": False
        },
        "max_daily_usage": {
            "type": "number",
            "title": "每日使用限制",
            "description": "每个用户每日最大使用次数",
            "default": 50,
            "min": 1,
            "max": 1000
        },
        "enable_nsfw": {
            "type": "boolean",
            "title": "启用NSFW内容",
            "description": "是否允许生成NSFW内容",
            "default": False
        },
        "enable_auto_save": {
            "type": "boolean",
            "title": "启用自动保存",
            "description": "自动保存生成的图片",
            "default": False
        },
        "show_params": {
            "type": "boolean",
            "title": "显示生成参数",
            "description": "在结果中显示详细的生成参数",
            "default": True
        },
        "image_proxy": {
            "type": "string",
            "title": "图片代理",
            "description": "图片下载代理地址（可选）",
            "default": "",
            "required": False
        },
        "api_proxy": {
            "type": "string",
            "title": "API代理",
            "description": "NovelAI API代理地址（可选）",
            "default": "",
            "required": False
        }
    }

    def __init__(self, context: Context, config: AstrBotConfig):  # 正确的参数
        super().__init__(context)
        self.context = context
        self.config = config  # AstrBotConfig 对象，由 AstrBot 自动传入

        # 初始化管理器
        self.cache = CacheManager()
        self.http_manager = HttpClientManager()
        self.config_manager = ConfigManager(config)
        
        # 从配置读取基础参数 - 使用常量作为默认值
        self.api_token = self.config.get('api_token', '')
        self.default_model = self.config.get('default_model', Constants.DEFAULT_MODEL)
        self.default_resolution = self.config.get('default_resolution', Constants.DEFAULT_RESOLUTION)
        self.default_steps = int(self.config.get('default_steps', Constants.DEFAULT_STEPS))
        self.default_scale = int(self.config.get('default_scale', Constants.DEFAULT_SCALE))
        self.default_sampler = self.config.get('default_sampler', Constants.DEFAULT_SAMPLER)
        self.enable_nsfw = bool(self.config.get('enable_nsfw', False))
        self.max_daily_usage = int(self.config.get('max_daily_usage', Constants.DEFAULT_MAX_DAILY_USAGE))
        self.cooldown_seconds = int(self.config.get('cooldown_seconds', Constants.DEFAULT_COOLDOWN))
        self.img2img_strength = float(self.config.get('img2img_strength', 0.7))  # 修改默认值为0.7
        self.img2img_noise = float(self.config.get('img2img_noise', 0.2))
        
        # 白名单配置
        self.whitelist_groups = self._load_whitelist()
        
        # 代理配置
        self.image_proxy = self.config.get('image_proxy', '')
        self.api_proxy = self.config.get('api_proxy', '')
        
        # 预设画风配置
        self.enable_style_preset = bool(self.config.get('enable_style_preset', False))
        self.selected_style_preset = self.config.get('selected_style_preset', '')
        
        # 负面提示词配置
        self.enable_negative_prompt = bool(self.config.get('enable_negative_prompt', False))
        self.selected_negative_prompt = self.config.get('selected_negative_prompt', '')
        
        # 管理员命令配置
        self.admin_only_commands = self._load_admin_commands()
        
        # 自动保存配置
        self.enable_auto_save = bool(self.config.get('enable_auto_save', False))
        self.save_path = self.config.get('save_path', Constants.DEFAULT_SAVE_PATH)
        
        # 显示参数配置
        self.show_params = bool(self.config.get('show_params', True))
        
        # 创建保存目录
        if self.enable_auto_save:
            if Utils.create_directory_if_not_exists(self.save_path):
                logger.info(f"自动保存已启用，保存路径: {self.save_path}")
            else:
                logger.error(f"无法创建保存目录: {self.save_path}")
                self.enable_auto_save = False
        
        # 预设配置 - 从JSON字符串解析
        self._load_presets()
        self._load_preset_categories()  # 加载预设分类
        self._load_style_presets()
        self._load_negative_prompts()
        
        # 初始化使用量管理器
        self.usage_manager = UsageManager(self.max_daily_usage, self.cooldown_seconds)

        # 清理过期数据（保留30天）
        self.usage_manager.cleanup_old_data(30)

        # 初始化衣服管理器
        clothing_db_path = os.path.join(os.path.dirname(__file__), 'clothing_database.txt')
        self.clothing_manager = ClothingManager(clothing_db_path)
        
        # 添加最后一次画图命令的记录 - 持久化存储
        self.last_draw_commands_file = os.path.join("data", "last_draw_commands.json")
        self.last_draw_commands = self._load_last_draw_commands()

        # 伪造转发开关配置
        self.enable_fake_forward_presets = self.config.get('enable_fake_forward_presets', True)

        # 使用配置管理器加载配置
        translate_config = self.config_manager.get_baidu_translate_config()
        self.baidu_translate_appid = translate_config['appid']
        self.baidu_translate_secret = translate_config['secret']
        self.enable_auto_translate = translate_config['enable_auto']
        self.baidu_translate_api_type = translate_config['api_type']

        audit_config = self.config_manager.get_baidu_audit_config()
        self.baidu_audit_api_key = audit_config['api_key']
        self.baidu_audit_secret_key = audit_config['secret_key']
        self.enable_image_audit = audit_config['enable']
        self.audit_failed_save_path = audit_config['save_path']
        self.audit_conclusion_type = audit_config['conclusion_type']
        self.audit_enable_politics = audit_config['enable_politics']
        self.audit_enable_porn = audit_config['enable_porn']
        self.audit_enable_terror = audit_config['enable_terror']
        self.audit_enable_disgust = audit_config['enable_disgust']

        # 创建审核失败图片保存目录
        if self.enable_image_audit:
            if Utils.create_directory_if_not_exists(self.audit_failed_save_path):
                logger.info(f"图片审核已启用，审核失败图片保存路径: {self.audit_failed_save_path}")
                logger.info(f"审核严格程度: {self.audit_conclusion_type} (1=严格, 2=中等, 3=宽松)")
            else:
                logger.error(f"无法创建审核失败图片保存目录: {self.audit_failed_save_path}")
                self.enable_image_audit = False
        
        # 验证配置
        if not self.api_token:
            logger.warning("NovelAI API Token未配置，插件功能受限")
        
        if not self.default_model:
            logger.info("NovelAI插件已加载，模型名称未设置，请在管理面板配置")
        else:
            logger.info(f"NovelAI插件已加载，模型: {self.default_model}")
            
        if self.image_proxy:
            logger.info(f"图片代理: {self.image_proxy}")
        if self.api_proxy:
            logger.info(f"API代理: {self.api_proxy}")
            
        if self.whitelist_groups:
            logger.info(f"白名单群组: {self.whitelist_groups}")
        else:
            logger.info("未设置群组白名单，所有群组均可使用")
            
        logger.info(f"已加载 {len(self.presets)} 个预设: {', '.join(self.presets.keys())}")
        logger.info(f"已加载 {len(self.preset_categories)} 个预设分类: {', '.join(self.preset_categories.keys())}")
        logger.info(f"已加载 {len(self.style_presets)} 个预设画风: {', '.join(self.style_presets.keys())}")
        logger.info(f"已加载 {len(self.negative_prompts)} 个负面提示词预设: {', '.join(self.negative_prompts.keys())}")
        
        if self.enable_style_preset and self.selected_style_preset:
            logger.info(f"当前启用预设画风: {self.selected_style_preset}")
            
        if self.enable_negative_prompt and self.selected_negative_prompt:
            logger.info(f"当前启用负面提示词: {self.selected_negative_prompt}")
            
        if self.admin_only_commands:
            logger.info(f"管理员限制命令: {', '.join(self.admin_only_commands)}")

    @classmethod
    def get_config_schema(cls):
        """返回配置模式，用于管理面板显示"""
        return cls.CONFIG_SCHEMA

    def validate_config(self, config_data: dict) -> dict:
        """验证配置数据"""
        errors = {}

        # 验证API Token
        if not config_data.get('api_token', '').strip():
            errors['api_token'] = "API Token不能为空"

        # 验证百度翻译配置
        appid = config_data.get('baidu_translate_appid', '').strip()
        secret = config_data.get('baidu_translate_secret', '').strip()

        if appid and not secret:
            errors['baidu_translate_secret'] = "设置了APP ID但未设置密钥"
        elif secret and not appid:
            errors['baidu_translate_appid'] = "设置了密钥但未设置APP ID"

        # 验证百度审核配置
        audit_api_key = config_data.get('baidu_audit_api_key', '').strip()
        audit_secret_key = config_data.get('baidu_audit_secret_key', '').strip()

        if audit_api_key and not audit_secret_key:
            errors['baidu_audit_secret_key'] = "设置了API Key但未设置Secret Key"
        elif audit_secret_key and not audit_api_key:
            errors['baidu_audit_api_key'] = "设置了Secret Key但未设置API Key"

        # 验证审核失败保存路径
        audit_path = config_data.get('audit_failed_save_path', '').strip()
        if audit_path and not Utils.create_directory_if_not_exists(audit_path):
            errors['audit_failed_save_path'] = "无效的保存路径"

        # 验证审核严格程度
        conclusion_type = config_data.get('audit_conclusion_type', 2)
        if not isinstance(conclusion_type, int) or conclusion_type < 1 or conclusion_type > 3:
            errors['audit_conclusion_type'] = "审核严格程度必须在1-3之间"

        # 验证数值范围
        steps = config_data.get('default_steps', 28)
        if not isinstance(steps, int) or steps < 1 or steps > 50:
            errors['default_steps'] = "步数必须在1-50之间"

        scale = config_data.get('default_scale', 7)
        if not isinstance(scale, int) or scale < 1 or scale > 20:
            errors['default_scale'] = "权重必须在1-20之间"

        daily_usage = config_data.get('max_daily_usage', 50)
        if not isinstance(daily_usage, int) or daily_usage < 1 or daily_usage > 1000:
            errors['max_daily_usage'] = "每日使用限制必须在1-1000之间"

        return errors

    def on_config_changed(self, new_config: dict):
        """配置更改时的回调"""
        # 更新实例变量
        self.api_token = new_config.get('api_token', '')
        self.default_model = new_config.get('default_model', '')
        self.default_resolution = new_config.get('default_resolution', '832x1216')
        self.default_steps = int(new_config.get('default_steps', 28))
        self.default_scale = int(new_config.get('default_scale', 7))
        self.baidu_translate_appid = new_config.get('baidu_translate_appid', '')
        self.baidu_translate_secret = new_config.get('baidu_translate_secret', '')
        self.enable_auto_translate = bool(new_config.get('enable_auto_translate', False))
        self.baidu_translate_api_type = new_config.get('baidu_translate_api_type', 'general')
        self.baidu_audit_api_key = new_config.get('baidu_audit_api_key', '')
        self.baidu_audit_secret_key = new_config.get('baidu_audit_secret_key', '')
        self.enable_image_audit = bool(new_config.get('enable_image_audit', False))
        self.audit_failed_save_path = new_config.get('audit_failed_save_path', 'novelai_audit_failed')
        self.audit_conclusion_type = int(new_config.get('audit_conclusion_type', 2))
        self.audit_enable_politics = bool(new_config.get('audit_enable_politics', True))
        self.audit_enable_porn = bool(new_config.get('audit_enable_porn', True))
        self.audit_enable_terror = bool(new_config.get('audit_enable_terror', True))
        self.audit_enable_disgust = bool(new_config.get('audit_enable_disgust', True))

        # 创建审核失败图片保存目录
        if self.enable_image_audit:
            Utils.create_directory_if_not_exists(self.audit_failed_save_path)
        self.max_daily_usage = int(new_config.get('max_daily_usage', 50))
        self.enable_nsfw = bool(new_config.get('enable_nsfw', False))
        self.enable_auto_save = bool(new_config.get('enable_auto_save', False))
        self.show_params = bool(new_config.get('show_params', True))
        self.image_proxy = new_config.get('image_proxy', '')
        self.api_proxy = new_config.get('api_proxy', '')

        # 图生图参数
        self.img2img_strength = float(new_config.get('img2img_strength', 0.7))
        self.img2img_noise = float(new_config.get('img2img_noise', 0.2))

        # 更新使用量管理器的限制
        if hasattr(self, 'usage_manager'):
            self.usage_manager.max_daily_usage = self.max_daily_usage

        logger.info("配置已更新")

    async def cleanup_resources(self):
        """清理资源"""
        try:
            await self.http_manager.close_all()
            self.cache.clear()
            self.config_manager.clear_cache()
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {e}")

    def _load_whitelist(self) -> Set[str]:
        """加载白名单群组"""
        whitelist_str = self.config.get('whitelist_groups', '')
        if not whitelist_str:
            return set()
        
        # 解析逗号分隔的群号
        groups = set()
        for group_id in whitelist_str.split(','):
            group_id = group_id.strip()
            if group_id:
                groups.add(group_id)
        
        return groups

    def _load_admin_commands(self) -> Set[str]:
        """加载仅管理员可用的命令"""
        admin_commands_str = self.config.get('admin_only_commands', '')
        if not admin_commands_str:
            return set()
        
        # 解析逗号分隔的命令
        commands = set()
        for cmd in admin_commands_str.split(','):
            cmd = cmd.strip()
            if cmd:
                commands.add(cmd)
        
        return commands

    def _check_whitelist(self, event) -> bool:
        """检查是否在白名单中"""
        # 如果没有设置白名单，允许所有群组
        if not self.whitelist_groups:
            return True
        
        # 获取群号
        try:
            # 根据不同的事件类型获取群号
            group_id = None
            if hasattr(event, 'group_id'):
                group_id = str(event.group_id)
            elif hasattr(event, 'get_group_id'):
                group_id = str(event.get_group_id())
            elif hasattr(event, 'message_obj') and hasattr(event.message_obj, 'group_id'):
                group_id = str(event.message_obj.group_id)
            
            # 如果是私聊消息，默认允许
            if not group_id or group_id == "0":
                return True
                
            # 检查是否在白名单中
            return group_id in self.whitelist_groups
            
        except Exception as e:
            logger.error(f"检查白名单时出错: {e}")
            return False

    def _check_admin_permission(self, event, command_name: str) -> bool:
        """检查是否需要管理员权限"""
        # 如果命令不在管理员限制列表中，所有人都可以使用
        if command_name not in self.admin_only_commands:
            return True
        
        # 检查是否为管理员
        try:
            # 获取发送者ID
            sender_id = str(event.get_sender_id())
            
            # 从 AstrBot 主配置中获取管理员列表
            # 获取 AstrBot 的主配置（不是插件配置）
            astrbot_config = self.context.get_config()
            admins = astrbot_config.get('admins_id', [])
            
            # 确保管理员ID都是字符串类型进行比较
            admins_str = [str(admin_id) for admin_id in admins]
            
            # 检查发送者是否在管理员列表中
            if sender_id in admins_str:
                logger.info(f"用户 {sender_id} 是管理员，允许使用命令 {command_name}")
                return True
            
            # 不是管理员
            logger.info(f"用户 {sender_id} 尝试使用管理员命令 {command_name}，但不在管理员列表中")
            return False
            
        except Exception as e:
            logger.error(f"检查管理员权限时出错: {e}")
            return False

    async def _save_image_locally(self, image_data: bytes, image_type: str = "txt2img") -> Optional[str]:
        """保存图片到本地"""
        if not self.enable_auto_save:
            return None
        
        try:
            # 生成文件名 - 只使用时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}.png"
            filepath = os.path.join(self.save_path, filename)
            
            # 保存图片
            with open(filepath, 'wb') as f:
                f.write(image_data)
            
            logger.info(f"图片已保存: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"保存图片失败: {e}")
            return None

    def _load_presets(self):
        """从配置加载预设"""
        # 直接检查配置项是否存在
        if 'presets' not in self.config:
            # 第一次使用，初始化默认预设
            default_presets = {
                "动漫": "masterpiece, best quality, anime style, official art, 1girl, solo, looking at viewer",
                "写实": "photorealistic, realistic, detailed, high quality, professional photography",
                "水彩": "watercolor, soft colors, artistic, traditional art, delicate brushstrokes",
                "油画": "oil painting, classical art, rich colors, detailed brushwork, fine art"
            }
            presets_json = json.dumps(default_presets, ensure_ascii=False)
            self.config['presets'] = presets_json
            self.config.save_config()  # 使用 AstrBotConfig 的保存方法
            self.presets = default_presets
            logger.info("首次使用，已初始化默认预设")
        else:
            # 配置存在，使用工具方法安全加载
            presets_json = self.config.get('presets', '{}')
            self.presets = Utils.safe_json_loads(presets_json, {})
            if self.presets:
                logger.info(f"成功加载预设配置: {len(self.presets)} 个预设")
            else:
                logger.warning("预设配置损坏，已清空预设列表")

    def _load_preset_categories(self):
        """从配置加载预设分类"""
        # 直接检查配置项是否存在
        if 'preset_categories' not in self.config:
            # 第一次使用，初始化默认分类
            # 使用常量中的默认分类，但只保留分类名称
            default_categories = {name: [] for name in Constants.DEFAULT_CATEGORIES.keys()}
            categories_json = json.dumps(default_categories, ensure_ascii=False)
            self.config['preset_categories'] = categories_json
            self.config.save_config()
            self.preset_categories = default_categories
            logger.info("首次使用，已初始化默认预设分类")
        else:
            # 配置存在，使用工具方法安全加载
            categories_json = self.config.get('preset_categories', '{}')
            self.preset_categories = Utils.safe_json_loads(
                categories_json,
                {name: [] for name in Constants.DEFAULT_CATEGORIES.keys()}
            )
            if self.preset_categories:
                logger.info(f"成功加载预设分类配置: {len(self.preset_categories)} 个分类")
            else:
                logger.warning("预设分类配置损坏，已重置为默认分类")

    def _load_style_presets(self):
        """从配置加载预设画风"""
        # 直接检查配置项是否存在
        if 'style_presets' not in self.config:
            # 第一次使用，初始化默认画风
            default_styles = {
                "清新淡雅": "soft lighting, pastel colors, gentle atmosphere, elegant composition",
                "赛博朋克": "cyberpunk, neon lights, futuristic, dark atmosphere, high contrast",
                "古风水墨": "traditional chinese painting, ink wash, elegant brushstrokes, classical style",
                "梦幻唯美": "dreamy atmosphere, soft focus, magical lighting, ethereal beauty"
            }
            style_presets_json = json.dumps(default_styles, ensure_ascii=False)
            self.config['style_presets'] = style_presets_json
            self.config.save_config()
            self.style_presets = default_styles
            logger.info("首次使用，已初始化默认画风")
        else:
            # 配置存在，加载配置的值
            style_presets_json = self.config.get('style_presets', '{}')
            try:
                self.style_presets = json.loads(style_presets_json)
                logger.info(f"成功加载画风配置: {len(self.style_presets)} 个画风")
            except json.JSONDecodeError as e:
                logger.error(f"预设画风配置JSON解析失败: {e}")
                # 解析失败时使用空字典
                self.style_presets = {}
                logger.warning("画风配置损坏，已清空画风列表")

    def _load_negative_prompts(self):
        """从配置加载负面提示词预设"""
        # 直接检查配置项是否存在
        if 'negative_prompts' not in self.config:
            # 第一次使用，初始化默认负面提示词
            default_negatives = {
                "低质量": "lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, worst quality, low quality, normal quality, jpeg artifacts",
                "水印签名": "signature, watermark, username, blurry, artist name, logo, trademark",
                "解剖错误": "bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, mutation, deformed, ugly, bad proportions, gross proportions",
                "动漫通用": "lowres, bad anatomy, bad hands, text, error, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry",
                "写实通用": "lowres, bad anatomy, bad hands, worst quality, low quality, blurry, cartoon, anime, sketches, CGI, 3D render",
                "严格质量": "lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, artist name, mutation, deformed, ugly, bad proportions, gross proportions, disfigured, out of frame, extra limbs, extra arms, extra legs, cloned face, duplicate"
            }
            negative_prompts_json = json.dumps(default_negatives, ensure_ascii=False)
            self.config['negative_prompts'] = negative_prompts_json
            self.config.save_config()
            self.negative_prompts = default_negatives
            logger.info("首次使用，已初始化默认负面提示词")
        else:
            # 配置存在，加载配置的值
            negative_prompts_json = self.config.get('negative_prompts', '{}')
            try:
                self.negative_prompts = json.loads(negative_prompts_json)
                logger.info(f"成功加载负面提示词配置: {len(self.negative_prompts)} 个预设")
            except json.JSONDecodeError as e:
                logger.error(f"负面提示词配置JSON解析失败: {e}")
                # 解析失败时使用空字典
                self.negative_prompts = {}
                logger.warning("负面提示词配置损坏，已清空负面提示词列表")

    def save_presets(self):
        """保存预设到配置文件"""
        try:
            # 将预设字典转换为JSON字符串
            presets_json = json.dumps(self.presets, ensure_ascii=False)
            # 使用AstrBot的配置系统保存
            self.config['presets'] = presets_json
            self.config.save_config()  # 使用 AstrBotConfig 的保存方法
            logger.info(f"预设已保存到配置文件，共 {len(self.presets)} 个预设")
        except Exception as e:
            logger.error(f"保存预设失败: {e}")

    def save_preset_categories(self):
        """保存预设分类到配置文件"""
        try:
            # 将分类字典转换为JSON字符串
            categories_json = json.dumps(self.preset_categories, ensure_ascii=False)
            # 使用AstrBot的配置系统保存
            self.config['preset_categories'] = categories_json
            self.config.save_config()
            logger.info(f"预设分类已保存到配置文件，共 {len(self.preset_categories)} 个分类")
        except Exception as e:
            logger.error(f"保存预设分类失败: {e}")

    def save_style_presets(self):
        """保存预设画风到配置文件"""
        try:
            # 将画风字典转换为JSON字符串
            style_presets_json = json.dumps(self.style_presets, ensure_ascii=False)
            # 使用AstrBot的配置系统保存
            self.config['style_presets'] = style_presets_json
            self.config.save_config()
            logger.info(f"预设画风已保存到配置文件，共 {len(self.style_presets)} 个画风")
        except Exception as e:
            logger.error(f"保存预设画风失败: {e}")

    def save_negative_prompts(self):
        """保存负面提示词预设到配置文件"""
        try:
            # 将负面提示词字典转换为JSON字符串
            negative_prompts_json = json.dumps(self.negative_prompts, ensure_ascii=False)
            # 使用AstrBot的配置系统保存
            self.config['negative_prompts'] = negative_prompts_json
            self.config.save_config()
            logger.info(f"负面提示词已保存到配置文件，共 {len(self.negative_prompts)} 个预设")
        except Exception as e:
            logger.error(f"保存负面提示词失败: {e}")

    def add_preset_to_category(self, preset_name: str, category: str):
        """将预设添加到指定分类"""
        if category not in self.preset_categories:
            self.preset_categories[category] = []

        # 从其他分类中移除（如果存在）
        for _, presets in self.preset_categories.items():
            if preset_name in presets:
                presets.remove(preset_name)

        # 添加到新分类
        if preset_name not in self.preset_categories[category]:
            self.preset_categories[category].append(preset_name)

        self.save_preset_categories()

    def remove_preset_from_categories(self, preset_name: str):
        """从所有分类中移除预设"""
        for _, presets in self.preset_categories.items():
            if preset_name in presets:
                presets.remove(preset_name)
        self.save_preset_categories()

    def get_presets_by_category(self, category: str) -> Dict[str, str]:
        """获取指定分类的预设"""
        if category not in self.preset_categories:
            return {}

        category_presets = {}
        for preset_name in self.preset_categories[category]:
            if preset_name in self.presets:
                category_presets[preset_name] = self.presets[preset_name]

        return category_presets

    def get_uncategorized_presets(self) -> Dict[str, str]:
        """获取未分类的预设"""
        categorized_presets = set()
        for presets in self.preset_categories.values():
            categorized_presets.update(presets)

        uncategorized = {}
        for preset_name, preset_content in self.presets.items():
            if preset_name not in categorized_presets:
                uncategorized[preset_name] = preset_content

        return uncategorized

    def _load_last_draw_commands(self) -> Dict[str, Dict[str, Any]]:
        """加载上次画图命令记录"""
        try:
            if os.path.exists(self.last_draw_commands_file):
                with open(self.last_draw_commands_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    logger.info(f"已加载 {len(data)} 个用户的画图命令记录")
                    return data
        except Exception as e:
            logger.error(f"加载画图命令记录失败: {e}")
        return {}

    def _save_last_draw_commands(self):
        """保存上次画图命令记录"""
        try:
            os.makedirs("data", exist_ok=True)
            with open(self.last_draw_commands_file, 'w', encoding='utf-8') as f:
                json.dump(self.last_draw_commands, f, ensure_ascii=False, indent=2)
            logger.debug("画图命令记录已保存")
        except Exception as e:
            logger.error(f"保存画图命令记录失败: {e}")

    def _record_draw_command(self, user_id: str, command: str, event_type: str, image_url: Optional[str] = None,
                           temp_strength: Optional[float] = None, temp_noise: Optional[float] = None):
        """记录画图命令"""
        self.last_draw_commands[user_id] = {
            "command": command,
            "event_type": event_type,
            "image_url": image_url,
            "temp_strength": temp_strength,
            "temp_noise": temp_noise,
            "timestamp": time.time()  # 添加时间戳
        }
        # 异步保存，避免阻塞主流程
        task = asyncio.create_task(self._async_save_draw_commands())
        # 添加异常处理回调
        task.add_done_callback(self._handle_save_task_result)

    def _handle_save_task_result(self, task):
        """处理保存任务的结果"""
        try:
            task.result()  # 获取任务结果，如果有异常会抛出
        except Exception as e:
            logger.error(f"异步保存任务异常: {e}")

    async def _async_save_draw_commands(self):
        """异步保存画图命令记录"""
        try:
            await asyncio.sleep(0.1)  # 短暂延迟，避免频繁写入
            self._save_last_draw_commands()
        except Exception as e:
            logger.error(f"异步保存画图命令失败: {e}")

    @performance_monitor("图片优化")
    async def _optimize_large_image(self, image_data: bytes) -> bytes:
        """优化大图片以防止API错误"""
        try:
            image_size = len(image_data)

            # 检查图片大小限制
            if image_size > Constants.MAX_IMAGE_SIZE:
                raise ImageProcessingError(f"图片过大 ({image_size / 1024 / 1024:.1f}MB)，超过限制 ({Constants.MAX_IMAGE_SIZE / 1024 / 1024:.1f}MB)")

            # 小于阈值直接返回
            if image_size < Constants.LARGE_IMAGE_THRESHOLD:
                return image_data

            logger.info(f"检测到大图片 ({image_size / 1024 / 1024:.1f}MB)，开始优化...")

            # 打开图片并检查尺寸
            img = PILImage.open(io.BytesIO(image_data))
            width, height = img.size

            # 检查图片尺寸限制
            if max(width, height) > Constants.MAX_IMAGE_DIMENSION:
                raise ImageProcessingError(f"图片尺寸过大 ({width}x{height})，超过限制 ({Constants.MAX_IMAGE_DIMENSION}px)")

            # 计算新尺寸（保持宽高比）
            max_size = 1536  # 最大边长

            if max(width, height) > max_size:
                scale_factor = max_size / max(width, height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)

                # 调整大小
                img = img.resize((new_width, new_height), PILImage.Resampling.LANCZOS)
                logger.info(f"图片尺寸调整: {width}x{height} -> {new_width}x{new_height}")

            # 保存为JPEG格式以减小文件大小
            output = io.BytesIO()
            if img.mode in ('RGBA', 'LA', 'P'):
                # 转换为RGB
                background = PILImage.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background

            # 使用较高质量保存
            img.save(output, format='JPEG', quality=85, optimize=True)
            optimized_data = output.getvalue()

            logger.info(f"图片优化完成: {image_size / 1024 / 1024:.1f}MB -> {len(optimized_data) / 1024 / 1024:.1f}MB")
            return optimized_data

        except Exception as e:
            logger.error(f"图片优化失败: {e}")
            # 如果是大小或尺寸限制错误，重新抛出
            if "过大" in str(e) or "超过限制" in str(e):
                raise
            return image_data  # 其他错误时返回原图

    async def _construct_image_message_for_redraw(self, event, image_url: str):
        """为重画功能构造包含图片的消息"""
        try:
            # 创建图片消息组件
            if image_url.startswith(('http://', 'https://')):
                image_comp = Comp.Image.fromURL(image_url)
            else:
                image_comp = Comp.Image.fromFileSystem(image_url)

            # 将图片组件添加到消息中
            if not hasattr(event.message_obj, 'message') or not event.message_obj.message:
                event.message_obj.message = []

            # 检查是否已经有图片，避免重复添加
            has_image = any(isinstance(seg, Comp.Image) for seg in event.message_obj.message)
            if not has_image:
                event.message_obj.message.append(image_comp)
                logger.info(f"为重画功能添加图片: {image_url}")

        except Exception as e:
            logger.error(f"构造重画图片消息失败: {e}")
            # 如果构造失败，不影响主流程，只是无法使用图片

    def _create_fake_forward_message(self, title: str, content: str, sender_name: str = "NovelAI助手") -> List:
        """创建伪造转发消息"""
        try:
            # 创建伪造转发的消息链
            # 使用Node组件来创建转发消息节点
            node = Comp.Node(
                uin=2854196310,  # 伪造的QQ号
                name=sender_name,
                content=[Comp.Plain(content)]
            )
            return [node]

        except Exception as e:
            logger.error(f"创建伪造转发消息失败: {e}")
            # 如果失败，返回普通文本
            return [Comp.Plain(f"📋 {title}\n\n{content}")]

    def _should_use_fake_forward_presets(self) -> bool:
        """检查是否应该使用预设的伪造转发"""
        return self.enable_fake_forward_presets

    @Utils.async_retry(max_attempts=2, delay=2.0)  # 减少重试次数，增加延迟
    async def translate_text(self, text: str, from_lang: str = 'zh', to_lang: str = 'en') -> Optional[str]:
        """使用百度翻译API翻译文本（支持通用版和词典版）"""
        if not self.baidu_translate_appid or not self.baidu_translate_secret:
            logger.warning("百度翻译API未配置，跳过翻译")
            return None

        # 根据配置选择不同的翻译接口
        api_type = getattr(self, 'baidu_translate_api_type', 'general')

        if api_type == 'dict':
            return await self._translate_with_dict_api(text, from_lang, to_lang)
        else:  # general (默认)
            return await self._translate_with_general_api(text, from_lang, to_lang)



    async def _translate_with_general_api(self, text: str, from_lang: str = 'zh', to_lang: str = 'en') -> Optional[str]:
        """使用通用版百度翻译API"""
        # 获取access_token
        access_token = await self.get_baidu_access_token()
        if not access_token:
            logger.error("无法获取百度access_token")
            raise Exception("无法获取百度access_token")

        # 构建请求参数
        params = {
            'q': text,
            'from': from_lang,
            'to': to_lang
        }

        # 发送请求
        client = self.http_manager.get_client("translate", 10.0)
        url = f"{Constants.BAIDU_TRANSLATE_GENERAL_API}?access_token={access_token}"
        response = await client.post(url, json=params)

        # 使用统一的响应处理器
        response_data = ApiResponseHandler.handle_http_response(response, "百度翻译API(通用版)")

        if response_data['success']:
            result = response_data['data']
            if isinstance(result, dict) and 'result' in result and 'trans_result' in result['result']:
                translated_text = result['result']['trans_result'][0]['dst']
                logger.info(f"翻译成功(通用版): {text[:50]}{'...' if len(text) > 50 else ''} -> {translated_text[:50]}{'...' if len(translated_text) > 50 else ''}")
                return translated_text
            else:
                error_msg = ApiResponseHandler.extract_error_message(response_data, "翻译结果格式错误")
                logger.error(f"百度翻译API(通用版)错误: {error_msg}")
                raise Exception(error_msg)
        else:
            error_msg = ApiResponseHandler.extract_error_message(response_data, "翻译请求失败")
            logger.error(f"百度翻译API(通用版)请求失败: {error_msg}")
            raise Exception(error_msg)

    async def _translate_with_dict_api(self, text: str, from_lang: str = 'zh', to_lang: str = 'en') -> Optional[str]:
        """使用词典版百度翻译API"""
        # 获取access_token
        access_token = await self.get_baidu_access_token()
        if not access_token:
            logger.error("无法获取百度access_token")
            raise Exception("无法获取百度access_token")

        # 构建请求参数
        params = {
            'q': text,
            'from': from_lang,
            'to': to_lang
        }

        # 发送请求
        client = self.http_manager.get_client("translate", 10.0)
        url = f"{Constants.BAIDU_TRANSLATE_DICT_API}?access_token={access_token}"
        response = await client.post(url, json=params)

        # 使用统一的响应处理器
        response_data = ApiResponseHandler.handle_http_response(response, "百度翻译API(词典版)")

        if response_data['success']:
            result = response_data['data']
            if isinstance(result, dict) and 'result' in result and 'trans_result' in result['result']:
                translated_text = result['result']['trans_result'][0]['dst']
                logger.info(f"翻译成功(词典版): {text[:50]}{'...' if len(text) > 50 else ''} -> {translated_text[:50]}{'...' if len(translated_text) > 50 else ''}")
                return translated_text
            else:
                error_msg = ApiResponseHandler.extract_error_message(response_data, "翻译结果格式错误")
                logger.error(f"百度翻译API(词典版)错误: {error_msg}")
                raise Exception(error_msg)
        else:
            error_msg = ApiResponseHandler.extract_error_message(response_data, "翻译请求失败")
            logger.error(f"百度翻译API(词典版)请求失败: {error_msg}")
            raise Exception(error_msg)

    def detect_chinese(self, text: str) -> bool:
        """检测文本是否包含中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))

    async def auto_translate_prompt(self, prompt: str) -> Tuple[str, bool]:
        """智能翻译提示词（只翻译非预设的中文部分）"""
        if not self.enable_auto_translate:
            return prompt, False

        if not self.detect_chinese(prompt):
            return prompt, False

        try:
            # 智能分离中文和英文部分
            translated_prompt = await self.smart_translate_prompt(prompt)
            if translated_prompt != prompt:
                return translated_prompt, True
            else:
                return prompt, False
        except Exception as e:
            # 如果翻译完全失败，返回原文并记录警告
            logger.warning(f"自动翻译失败，使用原文: {e}")
            return prompt, False

    async def smart_translate_prompt(self, prompt: str) -> str:
        """智能翻译：只翻译非预设的中文部分"""
        try:
            # 按逗号分割提示词
            parts = [part.strip() for part in prompt.split(',')]
            translated_parts = []

            for part in parts:
                if not part:
                    continue

                # 检查是否是预设名称（跳过翻译）
                is_preset = False
                for preset_name in self.presets.keys():
                    if part.lower() == preset_name.lower():
                        is_preset = True
                        break

                # 检查是否是画风名称（跳过翻译）
                if not is_preset:
                    for style_name in self.style_presets.keys():
                        if part.lower() == style_name.lower():
                            is_preset = True
                            break

                # 检查是否是负面提示词预设（跳过翻译）
                if not is_preset:
                    for preset_name in self.negative_prompts.keys():
                        if part.lower() == preset_name.lower():
                            is_preset = True
                            break

                # 如果是预设或不包含中文，直接保留
                if is_preset or not self.detect_chinese(part):
                    translated_parts.append(part)
                else:
                    # 只翻译包含中文的非预设部分
                    try:
                        translated_part = await self.translate_text(part)
                        if translated_part:
                            translated_parts.append(translated_part)
                        else:
                            translated_parts.append(part)
                    except Exception as e:
                        # 如果翻译失败（如API余额不足），保留原文
                        logger.debug(f"翻译部分 '{part}' 失败: {e}，保留原文")
                        translated_parts.append(part)

            return ', '.join(translated_parts)

        except Exception as e:
            logger.error(f"智能翻译失败: {e}")
            return prompt

    async def get_baidu_access_token(self) -> Optional[str]:
        """获取百度翻译API的access_token（带缓存）"""
        if not self.baidu_translate_appid or not self.baidu_translate_secret:
            return None

        # 生成缓存键
        cache_key = Utils.generate_cache_key("baidu_translate_token", self.baidu_translate_appid)

        # 尝试从缓存获取
        cached_token = self.cache.get(cache_key)
        if cached_token:
            logger.debug("使用缓存的百度翻译access_token")
            return cached_token

        try:
            params = {
                "grant_type": "client_credentials",
                "client_id": self.baidu_translate_appid,
                "client_secret": self.baidu_translate_secret
            }

            client = self.http_manager.get_client("baidu_auth", 10.0)
            response = await client.post(Constants.BAIDU_OAUTH_API, params=params)

            # 使用统一的响应处理器
            response_data = ApiResponseHandler.handle_http_response(response, "百度认证API")

            if response_data['success']:
                result = response_data['data']
                if isinstance(result, dict) and "access_token" in result:
                    token = result["access_token"]
                    # 缓存token，设置较短的TTL（比实际过期时间短一些）
                    self.cache.set(cache_key, token, ttl=1800)  # 30分钟
                    logger.info("百度API access_token获取成功并已缓存")
                    return token
                else:
                    logger.error(f"获取百度access_token失败: 响应中无access_token字段")
                    return None
            else:
                error_msg = ApiResponseHandler.extract_error_message(response_data, "认证请求失败")
                logger.error(f"百度API认证失败: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"获取百度翻译access_token异常: {e}")
            return None

    async def get_baidu_audit_access_token(self) -> Optional[str]:
        """获取百度审核API的access_token（带缓存）"""
        if not self.baidu_audit_api_key or not self.baidu_audit_secret_key:
            return None

        # 生成缓存键
        cache_key = Utils.generate_cache_key("baidu_audit_token", self.baidu_audit_api_key)

        # 尝试从缓存获取
        cached_token = self.cache.get(cache_key)
        if cached_token:
            logger.debug("使用缓存的百度审核access_token")
            return cached_token

        try:
            params = {
                "grant_type": "client_credentials",
                "client_id": self.baidu_audit_api_key,
                "client_secret": self.baidu_audit_secret_key
            }

            client = self.http_manager.get_client("baidu_audit_auth", 10.0)
            response = await client.post(Constants.BAIDU_OAUTH_API, params=params)

            # 使用统一的响应处理器
            response_data = ApiResponseHandler.handle_http_response(response, "百度审核认证API")

            if response_data['success']:
                result = response_data['data']
                if isinstance(result, dict) and "access_token" in result:
                    token = result["access_token"]
                    # 缓存token，设置较短的TTL（比实际过期时间短一些）
                    self.cache.set(cache_key, token, ttl=1800)  # 30分钟
                    logger.info("百度审核API access_token获取成功并已缓存")
                    return token
                else:
                    logger.error(f"获取百度审核access_token失败: 响应中无access_token字段")
                    return None
            else:
                error_msg = ApiResponseHandler.extract_error_message(response_data, "审核认证请求失败")
                logger.error(f"百度审核API认证失败: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"获取百度审核access_token异常: {e}")
            return None

    async def audit_image_content(self, image_data: bytes) -> Tuple[bool, str]:
        """审核图片内容"""
        if not self.enable_image_audit:
            return True, "图片审核未启用"

        access_token = await self.get_baidu_audit_access_token()
        if not access_token:
            logger.warning("百度审核API未配置或认证失败，跳过图片审核")
            return True, "审核API未配置"

        try:
            # 将图片转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')

            url = f"{Constants.BAIDU_AUDIT_API}?access_token={access_token}"

            # 百度图片审核API使用form-data格式
            data = {
                "image": image_base64
            }

            # 添加审核场景参数
            scenes = []
            if self.audit_enable_politics:
                scenes.append("politics")
            if self.audit_enable_porn:
                scenes.append("porn")
            if self.audit_enable_terror:
                scenes.append("terror")
            if self.audit_enable_disgust:
                scenes.append("disgust")

            if scenes:
                data["scenes"] = ",".join(scenes)

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            client = self.http_manager.get_client("baidu_audit", 30.0)  # 图片审核可能需要更长时间
            response = await client.post(url, data=data, headers=headers)

            # 使用统一的响应处理器
            response_data = ApiResponseHandler.handle_http_response(response, "百度图片审核API")

            if response_data['success']:
                result = response_data['data']
                if not isinstance(result, dict):
                    logger.error("百度图片审核API返回数据格式错误")
                    return True, "审核API返回格式错误，允许通过"

                logger.info(f"百度图片审核API响应: {result}")

                # 检查是否有业务错误
                if "error_code" in result:
                    error_code = result.get("error_code")
                    error_msg = result.get("error_msg", "未知错误")
                    logger.error(f"百度图片审核API业务错误: {error_code} - {error_msg}")
                    return True, f"审核API错误({error_code})，允许通过"

                # 检查审核结果
                conclusion_type = result.get("conclusionType", 1)
                conclusion = result.get("conclusion", "")

                # 记录图片大小信息
                image_size = Utils.format_file_size(len(image_data))
                logger.info(f"图片审核完成: 大小={image_size}, 结论类型={conclusion_type}, 结论={conclusion}")

                # 根据配置的严格程度判断
                if conclusion_type == 1:
                    # 合规
                    return True, "图片内容合规"
                elif conclusion_type == 2:
                    # 不合规
                    return False, f"图片审核不通过: {conclusion}"
                elif conclusion_type == 3:
                    # 疑似 - 根据配置决定是否通过
                    if self.audit_conclusion_type <= 2:
                        # 严格模式或中等模式：疑似也拦截
                        return False, f"图片疑似违规: {conclusion}"
                    else:
                        # 宽松模式：疑似允许通过
                        return True, f"图片疑似违规但允许通过: {conclusion}"
                elif conclusion_type == 4:
                    # 审核失败
                    return True, "图片审核失败，允许通过"
                else:
                    return True, f"未知审核结果({conclusion_type})，允许通过"
            else:
                error_msg = ApiResponseHandler.extract_error_message(response_data, "审核请求失败")
                logger.error(f"百度图片审核API请求失败: {error_msg}")
                return True, "审核API请求失败，允许通过"

        except Exception as e:
            logger.error(f"图片审核异常: {e}")
            return True, "审核异常，允许通过"

    def _parse_temp_img2img_params(self, text: str) -> Tuple[str, Optional[float], Optional[float]]:
        """
        解析临时图生图参数
        格式: -s<强度> -n<噪声>
        返回: (清理后的文本, 强度值, 噪声值)
        """
        import re

        # 初始化返回值
        cleaned_text = text
        temp_strength = None
        temp_noise = None

        # 匹配 -s<数值> 格式的强度参数
        strength_pattern = r'-s([\d.]+)'
        strength_match = re.search(strength_pattern, text)
        if strength_match:
            try:
                temp_strength = float(strength_match.group(1))
                # 验证范围
                if not (0.1 <= temp_strength <= 1.0):
                    temp_strength = None
                else:
                    # 从文本中移除这个参数
                    cleaned_text = re.sub(strength_pattern, '', cleaned_text)
            except ValueError:
                temp_strength = None

        # 匹配 -n<数值> 格式的噪声参数
        noise_pattern = r'-n([\d.]+)'
        noise_match = re.search(noise_pattern, text)
        if noise_match:
            try:
                temp_noise = float(noise_match.group(1))
                # 验证范围
                if not (0.0 <= temp_noise <= 1.0):
                    temp_noise = None
                else:
                    # 从文本中移除这个参数
                    cleaned_text = re.sub(noise_pattern, '', cleaned_text)
            except ValueError:
                temp_noise = None

        # 清理多余的空格
        cleaned_text = ' '.join(cleaned_text.split())

        return cleaned_text, temp_strength, temp_noise

    async def _save_audit_failed_image(self, image_data: bytes, image_type: str = "unknown") -> Optional[str]:
        """保存审核失败的图片到指定路径"""
        if not self.enable_image_audit:
            return None

        try:
            # 确保目录存在
            Utils.create_directory_if_not_exists(self.audit_failed_save_path)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"audit_failed_{image_type}_{timestamp}.png"
            filepath = os.path.join(self.audit_failed_save_path, filename)

            # 保存图片
            with open(filepath, 'wb') as f:
                f.write(image_data)

            logger.info(f"审核失败图片已保存: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"保存审核失败图片时出错: {e}")
            return None

    def _smart_split_prompt(self, prompt: str) -> List[str]:
        """智能分割提示词：识别中文词汇、数字编号、英文短语"""
        import re

        parts = []
        # 按空格分割
        words = prompt.split()

        current_part = []
        for word in words:
            # 检查是否是预设名称
            is_preset = False
            for preset_name in self.presets.keys():
                if word.lower() == preset_name.lower():
                    is_preset = True
                    break

            # 检查是否是画风预设
            if not is_preset:
                for style_name in self.style_presets.keys():
                    if word.lower() == style_name.lower():
                        is_preset = True
                        break

            # 检查是否是数字编号（001-430）
            is_number = re.match(r'^(0\d{2}|[1-4]\d{2}|430)$', word)

            # 检查是否是中文词汇
            is_chinese = re.search(r'[\u4e00-\u9fff]', word)

            if is_preset or is_number or is_chinese:
                # 如果当前有累积的英文部分，先保存
                if current_part:
                    parts.append(' '.join(current_part))
                    current_part = []
                # 预设、编号、中文词汇作为独立部分
                parts.append(word)
            else:
                # 英文词汇累积
                current_part.append(word)

        # 处理剩余的英文部分
        if current_part:
            parts.append(' '.join(current_part))

        return [part.strip() for part in parts if part.strip()]

    def apply_preset_and_style(self, prompt: str) -> Tuple[str, Optional[str]]:
        """
        应用预设和检测画风，支持多个预设，支持逗号分隔
        返回: (处理后的prompt, 检测到的画风名称)
        """
        processed_prompt = prompt
        applied_presets = []
        detected_style = None
        
        # 先处理逗号：将中文逗号转为英文逗号
        normalized_prompt = prompt.replace('，', ',')

        # 改进的分割逻辑：优先按逗号分割，如果没有逗号则按空格分割中文部分
        if ',' in normalized_prompt:
            # 有逗号时按逗号分割
            parts = [part.strip() for part in normalized_prompt.split(',')]
        else:
            # 没有逗号时，智能分割：将连续的中文词作为独立部分
            parts = self._smart_split_prompt(normalized_prompt)
        
        # 对于第一部分，如果它包含多个词，检查是否开头的词是预设
        first_part_words = parts[0].split() if parts else []
        remaining_first_part = []
        
        # 检查第一部分开头的词是否是预设
        checked_words = set()  # 避免重复检查
        i = 0
        while i < len(first_part_words):
            word = first_part_words[i]
            if word in checked_words:
                i += 1
                continue
                
            found_preset = False
            # 检查单个词是否是预设
            for preset_name, preset_content in self.presets.items():
                if word.lower() == preset_name.lower():
                    applied_presets.append((preset_name, preset_content))
                    found_preset = True
                    checked_words.add(word)
                    logger.info(f"识别到预设: {preset_name}")
                    break
            
            # 检查单个词是否是画风
            if not found_preset:
                for style_name in self.style_presets.keys():
                    if word.lower() == style_name.lower():
                        detected_style = style_name
                        found_preset = True
                        checked_words.add(word)
                        logger.info(f"识别到画风: {style_name}")
                        break
            
            # 如果不是预设也不是画风，加入剩余部分
            if not found_preset:
                remaining_first_part.extend(first_part_words[i:])
                break
            
            i += 1
        
        # 重组第一部分的剩余内容
        if remaining_first_part:
            parts[0] = ' '.join(remaining_first_part)
        else:
            parts[0] = ''
        
        # 处理其他部分（逗号分隔的部分）
        final_parts = []
        for i, part in enumerate(parts):
            part = part.strip()
            if not part:
                continue
            
            if i == 0 and not part:  # 第一部分已经被预设消耗完了
                continue
                
            found_preset = False
            
            # 检查整个部分是否是预设
            for preset_name, preset_content in self.presets.items():
                if part.lower() == preset_name.lower():
                    applied_presets.append((preset_name, preset_content))
                    found_preset = True
                    logger.info(f"识别到预设: {preset_name}")
                    break
            
            # 检查整个部分是否是画风
            if not found_preset:
                for style_name in self.style_presets.keys():
                    if part.lower() == style_name.lower():
                        detected_style = style_name
                        found_preset = True
                        logger.info(f"识别到画风: {style_name}")
                        break
            
            # 如果不是预设也不是画风，保留原样
            if not found_preset:
                final_parts.append(part)
        
        # 构建最终的prompt
        prompt_parts = []
        
        # 添加所有预设内容
        if applied_presets:
            preset_prompts = [preset[1] for preset in applied_presets]
            prompt_parts.extend(preset_prompts)
        
        # 添加剩余的描述
        if final_parts:
            prompt_parts.extend(final_parts)
        
        # 组合所有部分
        if prompt_parts:
            processed_prompt = ', '.join(prompt_parts)
        else:
            processed_prompt = ''
        
        if applied_presets:
            logger.info(f"应用了 {len(applied_presets)} 个预设: {[p[0] for p in applied_presets]}")
        
        # 应用画风
        if detected_style and detected_style in self.style_presets:
            style_prompt = self.style_presets[detected_style]
            final_prompt = f"{processed_prompt}, {style_prompt}" if processed_prompt else style_prompt
            logger.info(f"应用检测到的画风 '{detected_style}'")
            return final_prompt, detected_style
        
        # 如果没有检测到画风，应用全局画风（如果启用）
        if self.enable_style_preset and self.selected_style_preset and self.selected_style_preset in self.style_presets:
            style_prompt = self.style_presets[self.selected_style_preset]
            final_prompt = f"{processed_prompt}, {style_prompt}" if processed_prompt else style_prompt
            logger.info(f"应用全局画风 '{self.selected_style_preset}'")
            return final_prompt, self.selected_style_preset
        
        return processed_prompt, None

    def get_negative_prompt(self) -> str:
        """获取当前的负面提示词"""
        if self.enable_negative_prompt and self.selected_negative_prompt and self.selected_negative_prompt in self.negative_prompts:
            negative_prompt = self.negative_prompts[self.selected_negative_prompt]
            logger.info(f"使用负面提示词预设 '{self.selected_negative_prompt}'")
            return negative_prompt
        return ""

    def get_api_url(self, endpoint: str = "generate-image") -> str:
        """获取API URL"""
        if self.api_proxy:
            return f"{self.api_proxy}/ai/{endpoint}"
        return f"https://image.novelai.net/ai/{endpoint}"

    def get_image_proxy_url(self, original_url: str) -> str:
        """获取图片代理URL"""
        if self.image_proxy and original_url.startswith('https://image.novelai.net'):
            return original_url.replace('https://image.novelai.net', self.image_proxy)
        return original_url

    def is_v4_model(self, model_name: str) -> bool:
        """检查是否为v4/v4.5模型"""
        v4_models = [
            'nai-diffusion-4',
            'nai-diffusion-4-curated-preview', 
            'nai-diffusion-4.5-full',
            'nai-diffusion-4-5-full'
        ]
        return any(v4_model in model_name.lower() for v4_model in v4_models)

    def create_v4_prompt_structure(self, prompt: str) -> dict:
        """为v4模型创建特殊的prompt结构"""
        return {
            "caption": {
                "base_caption": prompt,
                "char_captions": [],
                "scenery_captions": []
            },
            "use_coords": False,
            "use_order": True
        }

    def get_model_parameters(self, model_name: str, prompt: str, width: int, height: int, 
                           steps: int, scale: int, sampler: str) -> dict:
        """根据模型版本生成对应的参数格式"""
        
        # 获取负面提示词
        negative_prompt = self.get_negative_prompt()
        
        # 基础参数
        base_params = {
            "params_version": 3,
            "width": width,
            "height": height,
            "scale": scale,
            "sampler": sampler,
            "steps": steps,
            "n_samples": 1,
            "ucPreset": 0,
            "qualityToggle": True,
            "dynamic_thresholding": False,
            "controlnet_strength": 1,
            "legacy": False,
            "add_original_image": True,
            "cfg_rescale": 0,
            "noise_schedule": "karras",
            "legacy_v3_extend": False,
            "skip_cfg_above_sigma": None,
            "use_coords": False,
            "characterPrompts": [],
            "negative_prompt": negative_prompt,  # 应用负面提示词
            "reference_image_multiple": [],
            "reference_information_extracted_multiple": [],
            "reference_strength_multiple": [],
            "seed": random.randint(0, 4294967295)
        }
        
        if self.is_v4_model(model_name):
            # v4/v4.5模型需要特殊的prompt结构
            base_params.update({
                "v4_prompt": self.create_v4_prompt_structure(prompt),
                "v4_negative_prompt": {"caption": {"base_caption": negative_prompt, "char_captions": [], "scenery_captions": []}}
            })
        
        return base_params

    def extract_prompt_from_message(self, event) -> Tuple[str, Optional[str]]:
        """
        从消息中提取完整的prompt
        返回: (处理后的prompt, 检测到的画风)
        """
        try:
            # 获取原始消息文本
            message_text = event.message_str.strip()
            
            # 移除命令前缀和命令本身
            # 使用command触发器后，AstrBot会自动处理前缀
            # 但message_str仍然包含完整的文本，所以需要手动移除
            if message_text.startswith("/画图"):
                prompt = message_text[3:].strip()  # 移除 "/画图"
            elif message_text.startswith("画图"):
                prompt = message_text[2:].strip()  # 兼容无前缀的情况
            else:
                # 对于command触发器，有时候message_str可能不包含命令本身
                prompt = message_text
            
            # 处理回车问题 - 将所有换行符替换为空格
            prompt = prompt.replace('\n', ' ').replace('\r', ' ')
            # 清理多余的空格
            prompt = ' '.join(prompt.split())
            
            logger.info(f"提取的原始prompt (已处理回车): {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
            
            # 应用预设和检测画风
            final_prompt, detected_style = self.apply_preset_and_style(prompt)
            
            logger.info(f"处理后的prompt长度: {len(final_prompt)} 字符")
            
            return final_prompt, detected_style
            
        except Exception as e:
            logger.error(f"提取prompt失败: {e}")
            return "", None

    def extract_prompt_from_img2img_message(self, event) -> Tuple[str, Optional[str]]:
        """
        从图生图消息中提取prompt
        返回: (处理后的prompt, 检测到的画风)
        """
        try:
            message_text = event.message_str.strip()
            
            # 移除[图片]标记
            clean_text = re.sub(r'\$\$图片\$\$', '', message_text).strip()
            
            # 移除命令前缀和命令本身
            prompt = ""
            if clean_text.startswith("/图生图"):
                prompt = clean_text[4:].strip()  # 移除 "/图生图"
            elif clean_text.startswith("图生图"):
                prompt = clean_text[3:].strip()  # 兼容无前缀的情况
            elif clean_text.startswith("/画图"):
                prompt = clean_text[3:].strip()  # 从画图转换过来的情况
            elif clean_text.startswith("画图"):
                prompt = clean_text[2:].strip()  # 从画图转换过来的情况（无前缀）
            else:
                # 对于command触发器，有时候message_str可能不包含命令本身
                prompt = clean_text
            
            # 处理回车问题 - 将所有换行符替换为空格
            prompt = prompt.replace('\n', ' ').replace('\r', ' ')
            # 清理多余的空格
            prompt = ' '.join(prompt.split())
            
            logger.info(f"图生图提取的原始prompt (已处理回车): {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
            
            # 应用预设和检测画风
            if prompt:
                final_prompt, detected_style = self.apply_preset_and_style(prompt)
                logger.info(f"图生图处理后的prompt长度: {len(final_prompt)} 字符")
                return final_prompt, detected_style
            
            return prompt, None
            
        except Exception as e:
            logger.error(f"图生图提取prompt失败: {e}")
            return "", None

    def extract_image_from_response(self, response_data: bytes) -> Optional[bytes]:
        """从NovelAI响应中提取图片数据"""
        try:
            # 尝试直接作为图片数据
            try:
                img = PILImage.open(io.BytesIO(response_data))
                logger.info(f"直接识别为图片: {img.size}, 格式: {img.format}")
                return response_data
            except Exception:
                pass
            
            # 尝试作为ZIP文件处理
            try:
                with zipfile.ZipFile(io.BytesIO(response_data)) as zip_file:
                    file_list = zip_file.namelist()
                    logger.info(f"ZIP文件包含: {file_list}")
                    
                    for filename in file_list:
                        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):
                            image_data = zip_file.read(filename)
                            img = PILImage.open(io.BytesIO(image_data))
                            logger.info(f"从ZIP提取图片: {filename}, 大小: {img.size}, 格式: {img.format}")
                            return image_data
            except Exception as e:
                logger.debug(f"ZIP处理失败: {e}")
            
            # 尝试作为base64编码的JSON
            try:
                json_data = json.loads(response_data.decode('utf-8'))
                if 'image' in json_data:
                    image_b64 = json_data['image']
                    image_data = base64.b64decode(image_b64)
                    img = PILImage.open(io.BytesIO(image_data))
                    logger.info(f"从JSON base64提取图片: {img.size}, 格式: {img.format}")
                    return image_data
            except Exception as e:
                logger.debug(f"JSON处理失败: {e}")
            
            logger.info(f"响应数据前100字节: {response_data[:100]}")
            logger.info(f"响应数据类型检测失败，大小: {len(response_data)} bytes")
            
            # 如果所有尝试都失败，抛出图片处理错误
            raise NovelAIError.ImageProcessingError("无法识别返回的图片格式")
            
        except NovelAIError.ImageProcessingError as e:
            # 直接将自定义错误向上传递
            raise e
        except Exception as e:
            # 将其他错误转换为自定义错误
            raise NovelAIError.ImageProcessingError("图片提取失败", e)

    def extract_images_from_messages(self, messages):
        """从消息列表中提取图片URL"""
        image_urls = []
        
        for seg in messages:
            if isinstance(seg, Comp.Image):
                url = None
                if hasattr(seg, 'url') and seg.url:
                    url = seg.url
                elif hasattr(seg, 'file') and seg.file:
                    url = seg.file
                elif hasattr(seg, 'data') and seg.data:
                    if isinstance(seg.data, dict):
                        url = seg.data.get('url') or seg.data.get('file')
                
                if url:
                    url = self.get_image_proxy_url(url)
                    image_urls.append(url)
        
        return image_urls

    def get_reply_message_images(self, event):
        """获取被引用消息中的图片"""
        try:
            messages = event.message_obj.message
            
            for seg in messages:
                if isinstance(seg, Comp.Reply):
                    if hasattr(seg, 'chain') and seg.chain:
                        images = self.extract_images_from_messages(seg.chain)
                        if images:
                            return images
                    break
            
            return []
        except Exception as e:
            logger.error(f"获取引用消息图片失败: {e}")
            return []

    async def download_and_encode_image(self, image_url: str) -> Optional[str]:
        """下载图片并转换为base64"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                if image_url.startswith(('http://', 'https://')):
                    try:
                        response = await client.get(image_url)
                        response.raise_for_status()  # 检查HTTP错误
                        image_data = response.content
                    except httpx.HTTPStatusError as e:
                        raise NovelAIError.NetworkError(f"HTTP错误: {e.response.status_code}", e)
                    except httpx.RequestError as e:
                        raise NovelAIError.NetworkError(f"请求错误: {str(e)}", e)
                else:
                    # 本地文件
                    if not os.path.exists(image_url):
                        raise NovelAIError.ImageProcessingError(f"本地文件不存在: {image_url}")
                    with open(image_url, 'rb') as f:
                        image_data = f.read()
            
            # 验证图片格式
            try:
                img = PILImage.open(io.BytesIO(image_data))
                logger.info(f"图片验证成功: {img.size}, 格式: {img.format}")

                # 智能图片优化 - 处理尺寸和文件大小
                width, height = img.size
                file_size_mb = len(image_data) / 1024 / 1024

                # 设置目标限制
                max_dimension = 1536  # 降低最大尺寸限制，提高兼容性
                max_file_size_mb = 3.0  # 降低文件大小限制到3MB

                needs_resize = width > max_dimension or height > max_dimension
                needs_compress = file_size_mb > max_file_size_mb

                if needs_resize or needs_compress:
                    logger.info(f"图片优化前: 尺寸={width}x{height}, 大小={file_size_mb:.2f}MB")

                    # 如果需要调整尺寸
                    if needs_resize:
                        scale_ratio = min(max_dimension / width, max_dimension / height)
                        new_width = int(width * scale_ratio)
                        new_height = int(height * scale_ratio)

                        logger.info(f"调整尺寸: {width}x{height} → {new_width}x{new_height}")
                        img = img.resize((new_width, new_height), PILImage.Resampling.LANCZOS)

                    # 智能压缩质量选择
                    if file_size_mb > 8:
                        quality = 70  # 超大文件用低质量
                    elif file_size_mb > 5:
                        quality = 80  # 大文件用中等质量
                    elif file_size_mb > 3:
                        quality = 85  # 中等文件用较高质量
                    else:
                        quality = 90  # 小文件用高质量

                    # 保存优化后的图片
                    img_io = io.BytesIO()
                    # 强制使用JPEG格式以获得更好的压缩率
                    if img.mode in ('RGBA', 'LA', 'P'):
                        # 处理透明图片，转换为RGB
                        background = PILImage.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                        img = background

                    img.save(img_io, format='JPEG', quality=quality, optimize=True)
                    image_data = img_io.getvalue()

                    new_file_size_mb = len(image_data) / 1024 / 1024
                    logger.info(f"图片优化后: 大小={new_file_size_mb:.2f}MB (质量={quality})")

                    # 如果还是太大，进一步压缩
                    if new_file_size_mb > max_file_size_mb:
                        for retry_quality in [60, 50, 40]:
                            img_io = io.BytesIO()
                            img.save(img_io, format='JPEG', quality=retry_quality, optimize=True)
                            image_data = img_io.getvalue()
                            new_file_size_mb = len(image_data) / 1024 / 1024

                            logger.info(f"进一步压缩: 质量={retry_quality}, 大小={new_file_size_mb:.2f}MB")

                            if new_file_size_mb <= max_file_size_mb:
                                break

                    logger.info(f"✅ 图片优化完成: 最终大小={len(image_data)/1024/1024:.2f}MB")
            except Exception as e:
                raise NovelAIError.ImageProcessingError("无效的图片格式", e)
            
            return base64.b64encode(image_data).decode('utf-8')
        except NovelAIError.NetworkError as e:
            # 直接将自定义错误向上传递
            raise e
        except NovelAIError.ImageProcessingError as e:
            # 直接将自定义错误向上传递
            raise e
        except Exception as e:
            # 将其他错误转换为自定义错误
            raise NovelAIError.ImageProcessingError("图片下载失败", e)

    def _has_images_in_message(self, event) -> bool:
        """检查消息中是否包含图片"""
        try:
            messages = event.message_obj.message
            for seg in messages:
                if isinstance(seg, Comp.Image):
                    return True
            return False
        except Exception:
            return False

    def _check_reply_has_images(self, event) -> bool:
        """检查引用的消息中是否有图片"""
        try:
            messages = event.message_obj.message
            for seg in messages:
                if isinstance(seg, Comp.Reply):
                    if hasattr(seg, 'chain') and seg.chain:
                        for reply_seg in seg.chain:
                            if isinstance(reply_seg, Comp.Image):
                                return True
                    break
            return False
        except Exception:
            return False

    def _get_last_image_url(self, event) -> Optional[str]:
        """获取最近的图片URL（当前消息或引用消息）"""
        # 先从当前消息获取
        image_urls = self.extract_images_from_messages(event.message_obj.message)
        if image_urls:
            return image_urls[0]
        
        # 再从引用消息获取
        reply_images = self.get_reply_message_images(event)
        if reply_images:
            return reply_images[0]
        
        return None

    async def _handle_text_to_image(self, event):
        """处理文生图的核心逻辑"""
        user_id = event.get_sender_id()

        # 先获取原始消息，解析临时参数，再进行预设识别
        original_message = event.message_str.strip()  # 保存原始消息

        # 从原始消息中解析临时参数
        temp_strength = None
        temp_noise = None
        if original_message:
            cleaned_message, temp_strength, temp_noise = self._parse_temp_img2img_params(original_message)
            # 更新事件消息，移除临时参数
            event.message_str = cleaned_message

        # 现在进行预设识别和提示词提取
        prompt, detected_style = self.extract_prompt_from_message(event)

        # 记录这次的画图命令（使用清理后的消息，参数单独保存）
        self._record_draw_command(
            user_id,
            event.message_str.strip(),  # 清理后的消息，便于预设识别
            "text_to_image",
            self._get_last_image_url(event),
            temp_strength,
            temp_noise
        )
        
        # 检查是否有图片（当前消息或引用消息）
        has_current_images = self._has_images_in_message(event)
        has_reply_images = self._check_reply_has_images(event)
        
        # 如果消息包含图片或引用的消息有图片，自动转为图生图
        if has_current_images or has_reply_images:
            logger.info("检测到图片，自动转换为图生图模式")
            # 如果有临时参数，需要将它们重新添加到提示词部分，而不是整个消息
            if temp_strength is not None or temp_noise is not None:
                param_str = ""
                if temp_strength is not None:
                    param_str += f"-s{temp_strength}"
                if temp_noise is not None:
                    param_str += f"-n{temp_noise}"
                # 只将临时参数添加到提示词前面，保持命令格式正确
                # 从 "/画图 提示词" 变为 "/图生图 -s0.8-n0.4 提示词"
                if event.message_str.startswith('/画图'):
                    event.message_str = event.message_str.replace('/画图', f'/图生图 {param_str}', 1)
                elif event.message_str.startswith('画图'):
                    event.message_str = event.message_str.replace('画图', f'图生图 {param_str}', 1)
                logger.info(f"传递临时参数到图生图: {param_str}")

            async for result in self._handle_image_to_image(event):
                yield result
            return
        
        if not self.usage_manager.check_daily_limit(user_id):
            usage_info = self.usage_manager.get_usage_info(user_id)
            yield event.plain_result(f"❌ 今日使用次数已达上限 ({usage_info['today_usage']}/{usage_info['max_daily']})")
            return
            
        if not self.api_token:
            yield event.plain_result("❌ 请先在管理面板配置NovelAI API Token")
            return
        
        if not self.default_model:
            yield event.plain_result("❌ 请先在管理面板配置NovelAI模型名称")
            return
        
        # prompt 和临时参数已经在前面解析过了

        if not prompt.strip():
            yield event.plain_result("❌ 请提供图片描述\n用法: /画图 描述 或 /画图 预设名\n💡 如果同时发送图片，将自动切换为图生图模式\n💡 临时参数: -s<强度> -n<噪声> (如: /画图 -s0.6 美女)")
            return

        # 处理衣服标签（在翻译之前）
        original_prompt = prompt
        processed_prompt = self.clothing_manager.process_prompt(prompt)
        if processed_prompt != prompt:
            prompt = processed_prompt
            logger.info(f"衣服标签处理: {original_prompt} -> {prompt}")

        # 自动翻译提示词（如果包含中文且开启了自动翻译）
        translated_prompt, was_translated = await self.auto_translate_prompt(prompt)
        if was_translated:
            prompt = translated_prompt
            logger.info(f"自动翻译: {processed_prompt} -> {prompt}")

        # 使用新的排队系统，每个请求都会排队
        request_id, can_process, queue_position, estimated_time = await self.usage_manager.add_to_queue(user_id)
        
        if not can_process:
            # 显示排队信息
            usage_info = self.usage_manager.get_usage_info(user_id)
            queue_message = f"🕒 已加入生成队列\n📍 当前位置: 第{queue_position}位\n⏱️ 预计等待: {estimated_time}秒\n👥 总队列长度: {queue_position}"
            
            # 如果用户有多个请求在队列中，显示提示
            if usage_info['user_requests_in_queue'] > 1:
                queue_message += f"\n📌 您有 {usage_info['user_requests_in_queue']} 个请求在队列中"
            
            yield event.plain_result(queue_message)
            
            # 等待轮到这个请求
            await self.usage_manager.wait_for_turn(request_id)
            # 不再显示"轮到你了"的提示
        else:
            yield event.plain_result("🎨 正在绘制中，请稍候...")
        
        try:
            start_time = time.time()
            width, height = map(int, self.default_resolution.split('x'))
            
            # 修复：正确调用文生图API，只传递需要的参数
            image_data, meta_info = await self._call_novelai_txt2img(
                prompt=prompt,
                width=width,
                height=height,
                steps=self.default_steps,
                scale=self.default_scale,
                sampler=self.default_sampler
            )
            
            if image_data:
                elapsed_time = time.time() - start_time
                self.usage_manager.record_usage(user_id)
                
                try:
                    final_image_data = self.extract_image_from_response(image_data)
                    
                    if final_image_data:
                        img = PILImage.open(io.BytesIO(final_image_data))
                        logger.info(f"最终图片验证成功: {img.size}, 格式: {img.format}")
                        
                        # 图片内容审核
                        if self.enable_image_audit:
                            audit_passed, audit_message = await self.audit_image_content(final_image_data)
                            if not audit_passed:
                                # 审核不通过，保存到审核失败路径
                                await self._save_audit_failed_image(final_image_data, "txt2img")
                                yield event.plain_result(f"❌ {audit_message}")
                                return
                            logger.info(f"图片审核通过: {audit_message}")

                        # 自动保存图片
                        saved_path = await self._save_image_locally(
                            final_image_data,
                            "txt2img"
                        )
                        if saved_path:
                            logger.info(f"图片已保存到: {saved_path}")

                        # 根据 show_params 配置决定显示内容
                        if self.show_params:
                            model_version = "v4/v4.5" if self.is_v4_model(self.default_model) else "v3"

                            # 构建结果信息
                            style_info = ""
                            if detected_style:
                                style_info = f" | 画风: {detected_style}"
                            elif self.enable_style_preset and self.selected_style_preset:
                                style_info = f" | 画风: {self.selected_style_preset}"

                            # 添加负面提示词信息
                            negative_info = ""
                            if self.enable_negative_prompt and self.selected_negative_prompt:
                                negative_info = f" | 负面: {self.selected_negative_prompt}"

                            chain = [
                                Comp.At(qq=event.get_sender_id()),
                                Comp.Plain(f"\n🖼️ 图片生成完成！\n"),
                                Comp.Plain(f"📝 提示词: {prompt[:100]}{'...' if len(prompt) > 100 else ''}\n"),
                                Comp.Plain(f"⚙️ 参数: {self.default_steps}步 | 权重{self.default_scale} | {self.default_resolution}{style_info}{negative_info}\n"),
                                Comp.Plain(f"🤖 模型: {self.default_model} ({model_version})\n"),
                                Comp.Image.fromBytes(final_image_data),
                                Comp.Plain(f"\n🕐 耗时: {elapsed_time:.1f}秒")
                            ]
                        else:
                            # 简洁模式：只显示图片和时间
                            chain = [
                                Comp.At(qq=event.get_sender_id()),
                                Comp.Plain("\n"),
                                Comp.Image.fromBytes(final_image_data),
                                Comp.Plain(f"\n🕐 {elapsed_time:.1f}秒")
                            ]

                        yield event.chain_result(chain)
                    else:
                        yield event.plain_result("❌ 无法解析返回的图片数据")
                    
                except NovelAIError.ImageProcessingError as img_error:
                    logger.error(f"图片处理错误: {img_error}")
                    yield event.plain_result(f"❌ 图片处理失败: {str(img_error)}")
                except Exception as img_error:
                    logger.error(f"图片处理错误: {img_error}")
                    yield event.plain_result(f"❌ 图片处理失败: {str(img_error)}")
            else:
                # 处理不同类型的错误
                error_message = "❌ 图片生成失败"
                if "error_type" in meta_info:
                    error_type = meta_info["error_type"]
                    if error_type == "network":
                        error_message = f"❌ 网络连接错误: {meta_info['error']}"
                    elif error_type == "auth":
                        error_message = f"❌ API认证失败: 请检查Token是否正确"
                    elif error_type == "quota":
                        error_message = f"❌ API使用次数超限: 请稍后再试"
                    elif error_type == "server":
                        error_message = f"❌ NovelAI服务器错误: 请稍后再试"
                    elif error_type == "config":
                        error_message = f"❌ 配置错误: {meta_info['error']}"
                    elif error_type == "param":
                        error_message = f"❌ 参数错误: {meta_info['error']}"
                    else:
                        error_message = f"❌ 生成失败: {meta_info['error']}"
                else:
                    error_message = f"❌ 图片生成失败: {meta_info.get('error', '未知错误')}"
                
                yield event.plain_result(error_message)
                
        except Exception as e:
            logger.error(f"NovelAI文生图错误: {e}")
            yield event.plain_result(f"❌ 生成出错: {str(e)}")
        finally:
            self.usage_manager.finish_processing(request_id)
            # 开始处理队列中的下一个请求
            next_request_id = await self.usage_manager.start_next_in_queue()
            if next_request_id:
                logger.info(f"开始处理队列中的下一个请求: {next_request_id}")

    async def _handle_image_to_image(self, event):
        """处理图生图的核心逻辑 - 使用早期版本的有效实现"""
        user_id = event.get_sender_id()

        # 先获取原始消息，解析临时参数，再进行预设识别
        original_message = event.message_str.strip()  # 保存原始消息

        # 从原始消息中解析临时参数
        temp_strength = None
        temp_noise = None
        if original_message:
            cleaned_message, temp_strength, temp_noise = self._parse_temp_img2img_params(original_message)
            # 更新事件消息，移除临时参数
            event.message_str = cleaned_message

        # 现在进行预设识别和提示词提取
        prompt, detected_style = self.extract_prompt_from_img2img_message(event)

        # 记录这次的图生图命令（使用清理后的消息，参数单独保存）
        self._record_draw_command(
            user_id,
            event.message_str.strip(),  # 清理后的消息，便于预设识别
            "image_to_image",
            self._get_last_image_url(event),
            temp_strength,
            temp_noise
        )
        
        if not self.usage_manager.check_daily_limit(user_id):
            usage_info = self.usage_manager.get_usage_info(user_id)
            yield event.plain_result(f"❌ 今日使用次数已达上限 ({usage_info['today_usage']}/{usage_info['max_daily']})")
            return
            
        if not self.api_token:
            yield event.plain_result("❌ 请先在管理面板配置NovelAI API Token")
            return
        
        if not self.default_model:
            yield event.plain_result("❌ 请先在管理面板配置NovelAI模型名称")
            return
        
        # prompt 和临时参数已经在前面解析过了

        # 处理衣服标签（在翻译之前）
        if prompt:  # 只有当有提示词时才进行处理
            original_prompt = prompt
            processed_prompt = self.clothing_manager.process_prompt(prompt)
            if processed_prompt != prompt:
                prompt = processed_prompt
                logger.info(f"图生图衣服标签处理: {original_prompt} -> {prompt}")

        # 自动翻译提示词（如果包含中文且开启了自动翻译）
        if prompt:  # 只有当有提示词时才进行翻译
            translated_prompt, was_translated = await self.auto_translate_prompt(prompt)
            if was_translated:
                prompt = translated_prompt
                logger.info(f"图生图自动翻译: {processed_prompt} -> {prompt}")

        image_urls = []
        current_images = self.extract_images_from_messages(event.message_obj.message)
        image_urls.extend(current_images)
        
        if not image_urls:
            reply_images = self.get_reply_message_images(event)
            image_urls.extend(reply_images)
        
        if not image_urls:
            yield event.plain_result("❌ 请发送图片后使用此指令\n\n💡 支持格式:\n• 发送图片 + /图生图 [描述]\n• /图生图 [描述] + 图片\n• /画图 [描述] + 图片（自动转换）\n• 回复包含图片的消息 + /图生图 [描述]")
            return
        
        # 使用新的排队系统，每个请求都会排队
        request_id, can_process, queue_position, estimated_time = await self.usage_manager.add_to_queue(user_id)
        
        if not can_process:
            # 显示排队信息
            usage_info = self.usage_manager.get_usage_info(user_id)
            queue_message = f"🕒 已加入生成队列\n📍 当前位置: 第{queue_position}位\n⏱️ 预计等待: {estimated_time}秒\n👥 总队列长度: {queue_position}"
            
            # 如果用户有多个请求在队列中，显示提示
            if usage_info['user_requests_in_queue'] > 1:
                queue_message += f"\n📌 您有 {usage_info['user_requests_in_queue']} 个请求在队列中"
            
            yield event.plain_result(queue_message)
            
            # 等待轮到这个请求
            await self.usage_manager.wait_for_turn(request_id)
            # 不再显示"轮到你了"的提示
        else:
            # 检测到从画图命令转换过来
            original_command = "画图" if event.message_str.strip().startswith(("/画图", "画图")) else "图生图"
            status_message = f"🎨 正在进行图生图，请稍候..."
            yield event.plain_result(status_message)
        
        try:
            start_time = time.time()
            
            # 下载参考图片
            try:
                reference_image = await self.download_and_encode_image(image_urls[0])
                if not reference_image:
                    yield event.plain_result("❌ 无法下载参考图片，请检查图片是否有效")
                    return
            except Exception as img_error:
                logger.error(f"下载参考图片失败: {img_error}")
                yield event.plain_result(f"❌ 下载参考图片失败: {str(img_error)}")
                return
            
            width, height = map(int, self.default_resolution.split('x'))
            
            # 如果没有提供描述，使用默认描述但仍然检测画风
            if not prompt:
                prompt = "best quality, amazing quality, very aesthetic"
                # 检测并应用画风
                prompt, detected_style = self.apply_preset_and_style(prompt)
                logger.info("图生图未提供描述，使用默认描述")
            
            # 使用临时参数（如果有）或配置中的默认值
            final_strength = temp_strength if temp_strength is not None else self.img2img_strength
            final_noise = temp_noise if temp_noise is not None else self.img2img_noise

            # 记录参数使用情况
            if temp_strength is not None or temp_noise is not None:
                logger.info(f"使用临时参数: 强度={final_strength}, 噪声={final_noise}")

            # 尝试调用图生图API
            try:
                image_data, meta_info = await self._call_novelai_img2img(
                    prompt=prompt,
                    reference_image=reference_image,
                    width=width,
                    height=height,
                    steps=self.default_steps,
                    scale=self.default_scale,
                    strength=final_strength,
                    noise=final_noise
                )
            except NovelAIError.ServerError as e:
                # 如果遇到500错误，尝试使用更保守的参数
                if "500" in str(e):
                    logger.warning("遇到500错误，尝试使用保守参数重试")
                    conservative_strength = min(final_strength, 0.6)  # 限制强度不超过0.6
                    conservative_noise = min(final_noise, 0.2)        # 限制噪声不超过0.2

                    logger.info(f"重试参数: 强度={conservative_strength}, 噪声={conservative_noise}")

                    image_data, meta_info = await self._call_novelai_img2img(
                        prompt=prompt,
                        reference_image=reference_image,
                        width=width,
                        height=height,
                        steps=self.default_steps,
                        scale=self.default_scale,
                        strength=conservative_strength,
                        noise=conservative_noise
                    )
                else:
                    # 其他服务器错误直接抛出
                    raise
            
            if image_data:
                elapsed_time = time.time() - start_time
                self.usage_manager.record_usage(user_id)
                
                try:
                    final_image_data = self.extract_image_from_response(image_data)
                    
                    if final_image_data:
                        img = PILImage.open(io.BytesIO(final_image_data))
                        logger.info(f"图生图验证成功: {img.size}, 格式: {img.format}")

                        # 图片内容审核
                        if self.enable_image_audit:
                            audit_passed, audit_message = await self.audit_image_content(final_image_data)
                            if not audit_passed:
                                # 审核不通过，保存到审核失败路径
                                await self._save_audit_failed_image(final_image_data, "img2img")
                                yield event.plain_result(f"❌ {audit_message}")
                                return
                            logger.info(f"图生图图片审核通过: {audit_message}")

                        # 自动保存图片
                        saved_path = await self._save_image_locally(
                            final_image_data,
                            "img2img"
                        )
                        if saved_path:
                            logger.info(f"图片已保存到: {saved_path}")
                        
                        # 根据 show_params 配置决定显示内容
                        if self.show_params:
                            model_version = "v4/v4.5" if self.is_v4_model(self.default_model) else "v3"

                            # 显示不同的标题根据原始命令
                            original_command = "画图" if event.message_str.strip().startswith(("/画图", "画图")) else "图生图"
                            title = "🖼️ 图生图完成！" if original_command == "图生图" else "🖼️ 图生图完成！（自动切换）"

                            # 构建结果信息
                            style_info = ""
                            if detected_style:
                                style_info = f" | 画风: {detected_style}"
                            elif self.enable_style_preset and self.selected_style_preset:
                                style_info = f" | 画风: {self.selected_style_preset}"

                            # 添加负面提示词信息
                            negative_info = ""
                            if self.enable_negative_prompt and self.selected_negative_prompt:
                                negative_info = f" | 负面: {self.selected_negative_prompt}"

                            chain = [
                                Comp.At(qq=event.get_sender_id()),
                                Comp.Plain(f"\n{title}\n"),
                                Comp.Plain(f"📝 提示词: {prompt[:100]}{'...' if len(prompt) > 100 else ''}\n"),
                                Comp.Plain(f"⚙️ 强度: {self.img2img_strength} | 噪声: {self.img2img_noise} | {self.default_steps}步 | {self.default_resolution}{style_info}{negative_info}\n"),
                                Comp.Plain(f"🤖 模型: {self.default_model} ({model_version})\n"),
                                Comp.Image.fromBytes(final_image_data),
                                Comp.Plain(f"\n🕐 耗时: {elapsed_time:.1f}秒")
                            ]
                        else:
                            # 简洁模式：只显示图片和时间
                            chain = [
                                Comp.At(qq=event.get_sender_id()),
                                Comp.Plain("\n"),
                                Comp.Image.fromBytes(final_image_data),
                                Comp.Plain(f"\n🕐 {elapsed_time:.1f}秒")
                            ]
                        
                        yield event.chain_result(chain)
                    else:
                        yield event.plain_result("❌ 无法解析返回的图片数据")
                    
                except Exception as img_error:
                    logger.error(f"图生图处理错误: {img_error}")
                    yield event.plain_result(f"❌ 图片处理失败: {str(img_error)}")
            else:
                # 处理不同类型的错误
                error_message = "❌ 图生图失败"
                if "error_type" in meta_info:
                    error_type = meta_info["error_type"]
                    if error_type == "network":
                        error_message = f"❌ 网络连接错误: {meta_info['error']}"
                    elif error_type == "auth":
                        error_message = f"❌ API认证失败: 请检查Token是否正确"
                    elif error_type == "quota":
                        error_message = f"❌ API使用次数超限: 请稍后再试"
                    elif error_type == "server":
                        error_message = f"❌ NovelAI服务器错误: 请稍后再试"
                    elif error_type == "config":
                        error_message = f"❌ 配置错误: {meta_info['error']}"
                    elif error_type == "param":
                        error_message = f"❌ 参数错误: {meta_info['error']}"
                    else:
                        error_message = f"❌ 图生图失败: {meta_info['error']}"
                else:
                    error_message = f"❌ 图生图失败: {meta_info.get('error', '未知错误')}"
                
                yield event.plain_result(error_message)
                
        except Exception as e:
            logger.error(f"NovelAI图生图错误: {e}")
            yield event.plain_result(f"❌ 生成出错: {str(e)}")
        finally:
            self.usage_manager.finish_processing(request_id)
            # 开始处理队列中的下一个请求
            next_request_id = await self.usage_manager.start_next_in_queue()
            if next_request_id:
                logger.info(f"开始处理队列中的下一个请求: {next_request_id}")

    # ========== 命令定义部分 ==========
    # 使用 filter.command 代替 filter.regex
    # filter.command 会自动处理唤醒前缀
    
    @filter.command("画图帮助")
    async def show_help(self, event):
        '''显示NovelAI插件使用帮助'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "画图帮助"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        help_text = (
            "📖 **NovelAI插件使用帮助**\n\n"
            "🎨 **基础绘图功能**\n"
            "• `/画图 <描述>` - 根据描述生成图片\n"
            "• `/画图 <预设名>` - 使用预设生成图片\n"
            "• `/画图 <预设名> <预设名2> <额外描述>` - 多预设+额外描述\n"
            "• `/画图 <描述> + 图片` - 自动切换为图生图\n"
            "• `/重画` - 重复上一次的画图命令\n\n"
            "🖼️ **图生图功能**\n"
            "• /图生图 <描述> + 图片 - 基于参考图生成\n"
            "• /图生图 + 图片 - 使用默认描述\n"
            "• 回复图片消息 + /图生图 <描述> - 基于回复图片\n\n"
            "⚙️ **图生图参数管理**\n"
            "• /图生图参数 - 查看当前参数设置\n"
            "• /图生图强度 <0.1-1.0> - 设置强度参数\n"
            "• /图生图噪声 <0.0-1.0> - 设置噪声参数\n"
            "• 临时参数: -s<强度> -n<噪声> (如: /画图 -s0.6 美女)\n\n"
            "🎭 **预设管理**\n"
            "• /添加预设 <名称> <提示词> - 添加新预设\n"
            "• /添加预设 <分类> <名称> <提示词> - 添加分类预设\n"
            "• /删除预设 <名称> - 删除指定预设\n"
            "• /预设列表 - 查看所有可用预设\n"
            "• /查询预设 <名称> - 查看预设详细内容\n\n"
            "📂 **分类预设**\n"
            "• /全部预设列表 - 按分类查看所有预设\n"
            "• /人物预设列表 - 查看人物分类预设\n"
            "• /衣服预设列表 - 查看衣服分类预设\n"
            "• /动作预设列表 - 查看动作分类预设\n"
            "• /场景预设列表 - 查看场景分类预设\n"
            "• /风格预设列表 - 查看风格分类预设\n"
            "• /其他预设列表 - 查看其他分类预设\n\n"
            "➕ **分类预设添加**\n"
            "• /添加人物预设 <名称> <提示词> - 添加人物预设\n"
            "• /添加衣服预设 <名称> <提示词> - 添加衣服预设\n"
            "• /添加动作预设 <名称> <提示词> - 添加动作预设\n"
            "• /添加场景预设 <名称> <提示词> - 添加场景预设\n"
            "• /添加风格预设 <名称> <提示词> - 添加风格预设\n"
            "• /添加其他预设 <名称> <提示词> - 添加其他预设\n"
            "• /其他预设列表 - 查看其他分类预设\n\n"
            "🔧 **分类管理**\n"
            "• /添加预设分类 <名称> - 添加新的预设分类\n"
            "• /预设分类列表 - 查看所有分类\n\n"
            "🖌️ **画风管理**\n"
            "• /添加画风 <名称> <描述> - 添加新画风\n"
            "• /删除画风 <名称> - 删除指定画风\n"
            "• /画风列表 - 查看所有可用画风\n"
            "• /查询画风 <名称> - 查看画风详细内容\n"
            "• /切换画风 <名称> - 切换当前画风\n"
            "• /启用画风 - 启用画风功能\n"
            "• /关闭画风 - 关闭画风功能\n\n"
            "🚫 **负面提示词管理**\n"
            "• /添加负面 <名称> <提示词> - 添加负面提示词\n"
            "• /删除负面 <名称> - 删除负面提示词\n"
            "• /负面列表 - 查看所有负面提示词\n"
            "• /查询负面 <名称> - 查看负面提示词详细内容\n"
            "• /切换负面 <名称> - 切换当前负面提示词\n"
            "• /启用负面 - 启用负面提示词功能\n"
            "• /关闭负面 - 关闭负面提示词功能\n\n"
            "📊 **统计和配置**\n"
            "• /画图配置 - 查看插件配置信息\n"
            "• /画图统计 - 查看全局使用统计\n\n"
            "📋 **转发显示**\n"
            "• /开启预设转发 - 预设列表以转发形式显示\n"
            "• /关闭预设转发 - 预设列表以普通形式显示\n\n"
            "🌐 **翻译功能**\n"
            "• /翻译 <文本> - 翻译中英文文本\n"
            "• /开启自动翻译 - 画图时自动翻译中文提示词\n"
            "• /关闭自动翻译 - 关闭画图自动翻译\n\n"
            "🔍 **图片审核**\n"
            "• /开启图片审核 - 启用图片内容合规性检测\n"
            "• /关闭图片审核 - 关闭图片审核\n\n"
            "🔍 **图片分析**\n"
            "• /nai参数 + 图片 - 提取NovelAI图片的生成参数\n\n"
            "💡 更多说明请使用 /画图使用说明 查看"
        )

        # 检查是否使用伪造转发
        if self._should_use_fake_forward_presets():
            forward_chain = self._create_fake_forward_message("NovelAI插件使用帮助", help_text, "NovelAI助手")
            yield event.chain_result(forward_chain)
        else:
            yield event.plain_result(help_text)

    @filter.command("画图使用说明")
    async def show_usage_guide(self, event):
        '''显示详细使用说明'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "画图使用说明"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        guide_text = (
            "📖 NovelAI插件详细使用说明\n\n"
            "📝 **使用示例**\n"
            "```\n"
            "/画图 美丽的风景\n"
            "/画图 动漫 可爱的猫咪\n"
            "/画图 守岸人 弗洛洛 动漫2  (多预设+画风)\n"
            "/画图 守岸人，弗洛洛，动漫2  (逗号分隔也可以)\n"
            "/添加预设 风格 赛博朋克 cyberpunk, neon lights, futuristic\n"
            "/添加预设 人物 可爱女孩 cute girl, kawaii, anime style\n"
            "/画图 赛博朋克 城市夜景\n"
            "/画图 可爱女孩 校园\n"
            "/添加画风 油画风 oil painting style, brush strokes\n"
            "/画图 山水风景 油画风  (自动使用油画风)\n"
            "/添加负面 动漫通用 lowres, bad anatomy, worst quality\n"
            "/切换负面 动漫通用\n"
            "/重画  (重复上一次的画图)\n"
            "[发送图片] /图生图 油画风格\n"
            "[回复图片] /画图 梦幻风格 (自动图生图)\n"
            "```\n\n"
            "🎨 **画风功能说明**\n"
            "画风会根据以下优先级应用：\n"
            "1. 提示词中包含的画风名（最高优先级）\n"
            "2. 全局设置的画风（如果启用）\n"
            "3. 不应用画风\n\n"
            "🚫 **负面提示词说明**\n"
            "负面提示词用于告诉AI避免生成的内容\n"
            "• 可以减少低质量、错误解剖等问题\n"
            "• 不同风格建议使用不同的负面提示词\n"
            "• 动漫风格和写实风格的负面词不同\n\n"
            "💡 **小贴士**\n"
            "• 支持多个预设组合，空格或逗号分隔都可以\n"
            "• 预设适合常用的角色或物体描述\n"
            "• 画风适合整体风格控制\n"
            "• 负面提示词帮助提高图片质量\n"
            "• 在提示词中写画风名可临时切换画风\n"
            "• 复制的tag中有回车也能正常使用\n"
            "• 使用 /重画 可快速重复上次生成\n"
            "• 详细的描述能获得更好的生成效果\n"
            "• 排队系统会显示等待时间和位置\n\n"
            "❓ **常见问题**\n"
            "Q: 如何临时使用不同画风？\n"
            "A: 在提示词中包含画风名即可，如: /画图 风景 油画风\n\n"
            "Q: 预设和画风有什么区别？\n"
            "A: 预设是具体的描述词，画风是风格控制，两者可叠加\n\n"
            "Q: 如何使用多个预设？\n"
            "A: 空格或逗号分隔即可，如: /画图 守岸人 弗洛洛 清新淡雅\n\n"
            "Q: 负面提示词有什么用？\n"
            "A: 避免生成不想要的内容，提高图片质量\n\n"
            "Q: 复制的提示词有换行怎么办？\n"
            "A: 插件会自动处理，无需手动删除换行\n\n"
            "Q: 重画功能能用于图生图吗？\n"
            "A: 可以！重画会自动使用上次的图片和参数\n\n"
            "更多问题请查看插件配置或联系管理员！"
        )

        # 检查是否使用伪造转发
        if self._should_use_fake_forward_presets():
            forward_chain = self._create_fake_forward_message("NovelAI插件详细使用说明", guide_text, "NovelAI助手")
            yield event.chain_result(forward_chain)
        else:
            yield event.plain_result(guide_text)

    @filter.command("重画")
    async def redraw(self, event):
        '''重复上一次的画图命令'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "重画"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        user_id = event.get_sender_id()

        # 检查是否有上次的命令
        if user_id not in self.last_draw_commands:
            yield event.plain_result("❌ 没有找到上一次的画图命令\n\n💡 请先使用 /画图 或 /图生图 命令")
            return

        last_cmd = self.last_draw_commands[user_id]

        # 创建一个新的事件对象，保持原有的命令文本
        event.message_str = last_cmd["command"]

        # 如果有保存的临时参数，添加到命令中
        temp_strength = last_cmd.get("temp_strength")
        temp_noise = last_cmd.get("temp_noise")
        if temp_strength is not None or temp_noise is not None:
            param_str = ""
            if temp_strength is not None:
                param_str += f"-s{temp_strength}"
            if temp_noise is not None:
                param_str += f"-n{temp_noise}"

            # 将参数添加到命令开头（在提示词之前）
            original_cmd = event.message_str
            if original_cmd.startswith("/画图 ") or original_cmd.startswith("/图生图 "):
                cmd_part = original_cmd.split(" ", 1)[0]  # "/画图" 或 "/图生图"
                rest_part = original_cmd.split(" ", 1)[1] if " " in original_cmd else ""
                event.message_str = f"{cmd_part} {param_str} {rest_part}".strip()
                logger.info(f"重画使用临时参数: {param_str}")

        # 根据上次的命令类型调用相应的处理方法
        if last_cmd["event_type"] == "text_to_image":
            # 检查是否有图片URL，如果有则转为图生图
            if last_cmd.get("image_url"):
                # 构造包含图片的消息
                await self._construct_image_message_for_redraw(event, last_cmd["image_url"])
                async for result in self._handle_image_to_image(event):
                    yield result
            else:
                # 纯文生图
                async for result in self._handle_text_to_image(event):
                    yield result
        elif last_cmd["event_type"] == "image_to_image":
            # 图生图重画
            if last_cmd.get("image_url"):
                # 构造包含图片的消息
                await self._construct_image_message_for_redraw(event, last_cmd["image_url"])
                async for result in self._handle_image_to_image(event):
                    yield result
            else:
                yield event.plain_result("❌ 重画图生图功能需要原始图片，但图片已丢失\n\n💡 请重新发送图片后使用 /图生图 命令")
        else:
            yield event.plain_result("❌ 未知的命令类型，无法重画")

    @filter.command("查询预设")
    async def query_preset(self, event):
        '''查询指定预设的详细内容'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "查询预设"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/查询预设"):
                preset_name = message_text[5:].strip()
            elif message_text.startswith("查询预设"):
                preset_name = message_text[4:].strip()
            else:
                preset_name = message_text
            
            if not preset_name:
                yield event.plain_result("❌ 用法: /查询预设 <预设名>\n\n💡 使用 /预设列表 查看所有可用预设")
                return
            
            if preset_name not in self.presets:
                yield event.plain_result(f"❌ 预设 '{preset_name}' 不存在")
                return
            
            preset_content = self.presets[preset_name]
            yield event.plain_result(
                f"📋 预设详情\n\n"
                f"🏷️ 名称: {preset_name}\n"
                f"📝 内容: {preset_content}\n"
                f"📏 长度: {len(preset_content)} 字符"
            )
            
        except Exception as e:
            logger.error(f"查询预设失败: {e}")
            yield event.plain_result(f"❌ 查询预设失败: {str(e)}")

    @filter.command("查询画风")
    async def query_style(self, event):
        '''查询指定画风的详细内容'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "查询画风"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/查询画风"):
                style_name = message_text[5:].strip()
            elif message_text.startswith("查询画风"):
                style_name = message_text[4:].strip()
            else:
                style_name = message_text
            
            if not style_name:
                yield event.plain_result("❌ 用法: /查询画风 <画风名>\n\n💡 使用 /画风列表 查看所有可用画风")
                return
            
            if style_name not in self.style_presets:
                yield event.plain_result(f"❌ 画风 '{style_name}' 不存在")
                return
            
            style_content = self.style_presets[style_name]
            yield event.plain_result(
                f"🎨 画风详情\n\n"
                f"🏷️ 名称: {style_name}\n"
                f"📝 内容: {style_content}"
            )
            
        except Exception as e:
            logger.error(f"查询画风失败: {e}")
            yield event.plain_result(f"❌ 查询画风失败: {str(e)}")

    @filter.command("查询负面")
    async def query_negative(self, event):
        '''查询指定负面提示词的详细内容'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "查询负面"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/查询负面"):
                negative_name = message_text[5:].strip()
            elif message_text.startswith("查询负面"):
                negative_name = message_text[4:].strip()
            else:
                negative_name = message_text
            
            if not negative_name:
                yield event.plain_result("❌ 用法: /查询负面 <名称>\n\n💡 使用 /负面列表 查看所有可用负面提示词")
                return
            
            if negative_name not in self.negative_prompts:
                yield event.plain_result(f"❌ 负面提示词 '{negative_name}' 不存在")
                return
            
            negative_content = self.negative_prompts[negative_name]
            yield event.plain_result(
                f"🚫 负面提示词详情\n\n"
                f"🏷️ 名称: {negative_name}\n"
                f"📝 内容: {negative_content}"
            )
            
        except Exception as e:
            logger.error(f"查询负面提示词失败: {e}")
            yield event.plain_result(f"❌ 查询负面提示词失败: {str(e)}")

    # 修复：画图命令使用 command 触发器
    @filter.command("画图")
    async def text_to_image(self, event):
        '''使用NovelAI根据描述生成图片（支持自动图生图）'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "画图"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        async for result in self._handle_text_to_image(event):
            yield result

    @filter.command("添加预设")
    async def add_preset(self, event):
        '''添加新的提示词预设'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "添加预设"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/添加预设"):
                args_text = message_text[5:].strip()
            elif message_text.startswith("添加预设"):
                args_text = message_text[4:].strip()
            else:
                args_text = message_text
            
            # 分割参数：支持可选的分类参数
            # 格式1: /添加预设 <预设名> <提示词>
            # 格式2: /添加预设 <分类> <预设名> <提示词>
            parts = args_text.split(None, 2)

            if len(parts) < 2:
                categories_list = ', '.join(self.preset_categories.keys())
                yield event.plain_result(
                    f"❌ 用法: \n"
                    f"• /添加预设 <预设名> <提示词>\n"
                    f"• /添加预设 <分类> <预设名> <提示词>\n\n"
                    f"📂 可用分类: {categories_list}\n\n"
                    f"例如: /添加预设 人物 可爱女孩 cute girl, kawaii, anime style"
                )
                return

            # 判断是否指定了分类
            if len(parts) == 3 and parts[0] in self.preset_categories:
                # 指定了分类
                category = parts[0]
                preset_name = parts[1]
                preset_prompt = parts[2]
            elif len(parts) >= 2:
                # 没有指定分类，归入"其他"
                category = "其他"
                preset_name = parts[0]
                preset_prompt = ' '.join(parts[1:]) if len(parts) > 2 else parts[1]
            else:
                categories_list = ', '.join(self.preset_categories.keys())
                yield event.plain_result(
                    f"❌ 参数不足\n\n"
                    f"用法: \n"
                    f"• /添加预设 <预设名> <提示词>\n"
                    f"• /添加预设 <分类> <预设名> <提示词>\n\n"
                    f"📂 可用分类: {categories_list}"
                )
                return
            
            if not preset_name or not preset_prompt:
                yield event.plain_result("❌ 预设名和提示词不能为空")
                return
            
            # 检查预设名是否已存在
            if preset_name in self.presets:
                # 单次回复，只显示原内容
                result_message = (
                    f"⚠️ 预设 '{preset_name}' 已存在，预设将被覆盖\n"
                    f"📂 分类: {category}\n"
                    f"原内容: {self.presets[preset_name]}"
                )
            else:
                # 新预设
                result_message = f"✅ 预设 '{preset_name}' 添加成功！\n📂 分类: {category}\n内容: {preset_prompt}"

            # 添加或更新预设
            self.presets[preset_name] = preset_prompt
            # 添加到分类
            self.add_preset_to_category(preset_name, category)
            # 保存预设到配置文件
            self.save_presets()

            yield event.plain_result(result_message)
            
        except Exception as e:
            logger.error(f"添加预设失败: {e}")
            yield event.plain_result(f"❌ 添加预设失败: {str(e)}")

    @filter.command("添加人物预设")
    async def add_character_preset(self, event):
        '''添加人物分类的预设'''
        async for result in self._add_category_preset(event, "人物", "添加人物预设"):
            yield result

    @filter.command("添加衣服预设")
    async def add_clothing_preset(self, event):
        '''添加衣服分类的预设'''
        async for result in self._add_category_preset(event, "衣服", "添加衣服预设"):
            yield result

    @filter.command("添加动作预设")
    async def add_action_preset(self, event):
        '''添加动作分类的预设'''
        async for result in self._add_category_preset(event, "动作", "添加动作预设"):
            yield result

    @filter.command("添加场景预设")
    async def add_scene_preset(self, event):
        '''添加场景分类的预设'''
        async for result in self._add_category_preset(event, "场景", "添加场景预设"):
            yield result

    @filter.command("添加风格预设")
    async def add_style_preset_category(self, event):
        '''添加风格分类的预设'''
        async for result in self._add_category_preset(event, "风格", "添加风格预设"):
            yield result

    @filter.command("添加其他预设")
    async def add_other_preset(self, event):
        '''添加其他分类的预设'''
        async for result in self._add_category_preset(event, "其他", "添加其他预设"):
            yield result

    async def _add_category_preset(self, event, category: str, command_name: str):
        '''通用的分类预设添加方法'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, command_name):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        try:
            # 从消息中提取预设名和内容
            message_text = event.message_str.strip()

            # 移除命令部分
            if message_text.startswith(f"/{command_name}"):
                content = message_text[len(f"/{command_name}"):].strip()
            elif message_text.startswith(command_name):
                content = message_text[len(command_name):].strip()
            else:
                content = message_text

            if not content:
                yield event.plain_result(f"❌ 请提供预设内容\n\n📖 用法: /{command_name} <预设名> <预设内容>\n💡 例如: /{command_name} 美少女 1girl, beautiful, cute")
                return

            # 解析预设名和内容
            parts = content.split(' ', 1)
            if len(parts) < 2:
                yield event.plain_result(f"❌ 格式错误\n\n📖 用法: /{command_name} <预设名> <预设内容>\n💡 例如: /{command_name} 美少女 1girl, beautiful, cute")
                return

            preset_name = parts[0].strip()
            preset_prompt = parts[1].strip()

            if not preset_name or not preset_prompt:
                yield event.plain_result(f"❌ 预设名和内容不能为空\n\n📖 用法: /{command_name} <预设名> <预设内容>")
                return

            # 检查预设名是否已存在
            if preset_name in self.presets:
                # 单次回复，只显示原内容
                result_message = (
                    f"⚠️ 预设 '{preset_name}' 已存在，预设将被覆盖\n"
                    f"📂 分类: {category}\n"
                    f"原内容: {self.presets[preset_name]}"
                )
            else:
                result_message = f"✅ 预设 '{preset_name}' 已添加到 '{category}' 分类"

            # 添加预设
            self.presets[preset_name] = preset_prompt

            # 保存预设
            presets_json = json.dumps(self.presets, ensure_ascii=False)
            self.config['presets'] = presets_json

            # 添加到指定分类
            self.add_preset_to_category(preset_name, category)

            # 保存配置
            self.config.save_config()

            yield event.plain_result(result_message)

        except Exception as e:
            logger.error(f"{command_name}失败: {e}")
            yield event.plain_result(f"❌ {command_name}失败: {str(e)}")

    @filter.command("删除预设")
    async def delete_preset(self, event):
        '''删除指定的提示词预设'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "删除预设"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/删除预设"):
                preset_name = message_text[5:].strip()
            elif message_text.startswith("删除预设"):
                preset_name = message_text[4:].strip()
            else:
                preset_name = message_text
            
            if not preset_name:
                yield event.plain_result("❌ 用法: /删除预设 <预设名>")
                return
            
            if preset_name not in self.presets:
                yield event.plain_result(f"❌ 预设 '{preset_name}' 不存在\n\n可用预设: {', '.join(self.presets.keys())}")
                return
            
            deleted_content = self.presets.pop(preset_name)
            # 从分类中移除
            self.remove_preset_from_categories(preset_name)
            # 重要：保存更新后的预设到配置文件
            self.save_presets()

            yield event.plain_result(f"✅ 预设 '{preset_name}' 删除成功！\n已删除内容: {deleted_content[:100]}{'...' if len(deleted_content) > 100 else ''}")
            
        except Exception as e:
            logger.error(f"删除预设失败: {e}")
            yield event.plain_result(f"❌ 删除预设失败: {str(e)}")

    @filter.command("预设列表")
    async def list_presets(self, event):
        '''显示所有可用的提示词预设'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "预设列表"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        if not self.presets:
            yield event.plain_result("📋 当前没有任何预设\n\n💡 使用 /添加预设 <名称> <内容> 来添加预设")
            return
        
        # 简化显示，只显示名称
        preset_names = list(self.presets.keys())
        preset_list = f"📋 预设列表: {', '.join(preset_names)}\n\n"
        preset_list += "💡 使用方法:\n• /画图 <预设名>\n• /画图 <预设名> <预设名2> <额外描述>\n• /图生图 <预设名>\n• /查询预设 <预设名> - 查看详细内容"
        
        yield event.plain_result(preset_list)

    @filter.command("全部预设列表")
    async def list_all_presets_by_category(self, event):
        '''按分类显示所有预设'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "全部预设列表"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        if not self.presets:
            yield event.plain_result("📋 当前没有任何预设\n\n💡 使用 /添加预设 <名称> <内容> 来添加预设")
            return

        result_text = "📋 全部预设列表 (按分类)\n\n"

        # 显示各分类的预设
        for category, preset_names in self.preset_categories.items():
            if preset_names:  # 只显示有预设的分类
                valid_presets = [name for name in preset_names if name in self.presets]
                if valid_presets:
                    result_text += f"📂 **{category}** ({len(valid_presets)}个)\n"
                    result_text += f"({', '.join(valid_presets)})\n\n"

        # 显示未分类的预设
        uncategorized = self.get_uncategorized_presets()
        if uncategorized:
            result_text += f"📂 **未分类** ({len(uncategorized)}个)\n"
            result_text += f"({', '.join(uncategorized.keys())})\n\n"

        result_text += f"💡 总计: {len(self.presets)} 个预设\n"
        result_text += "使用方法:\n• /人物预设列表 - 查看人物分类\n• /查询预设 <名称> - 查看详细内容"

        # 检查是否使用伪造转发
        if self._should_use_fake_forward_presets():
            forward_chain = self._create_fake_forward_message("全部预设列表", result_text, "预设管理器")
            yield event.chain_result(forward_chain)
        else:
            yield event.plain_result(result_text)

    @filter.command("人物预设列表")
    async def list_character_presets(self, event):
        '''显示人物分类的预设'''
        async for result in self._list_category_presets(event, "人物", "人物预设列表"):
            yield result

    @filter.command("衣服预设列表")
    async def list_clothing_presets(self, event):
        '''显示衣服分类的预设'''
        async for result in self._list_category_presets(event, "衣服", "衣服预设列表"):
            yield result

    @filter.command("动作预设列表")
    async def list_action_presets(self, event):
        '''显示动作分类的预设'''
        async for result in self._list_category_presets(event, "动作", "动作预设列表"):
            yield result

    @filter.command("场景预设列表")
    async def list_scene_presets(self, event):
        '''显示场景分类的预设'''
        async for result in self._list_category_presets(event, "场景", "场景预设列表"):
            yield result

    @filter.command("风格预设列表")
    async def list_style_presets_category(self, event):
        '''显示风格分类的预设'''
        async for result in self._list_category_presets(event, "风格", "风格预设列表"):
            yield result

    @filter.command("其他预设列表")
    async def list_other_presets(self, event):
        '''显示其他分类的预设'''
        async for result in self._list_category_presets(event, "其他", "其他预设列表"):
            yield result

    async def _list_category_presets(self, event, category: str, command_name: str):
        '''通用的分类预设列表显示方法'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, command_name):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        category_presets = self.get_presets_by_category(category)

        if not category_presets:
            yield event.plain_result(f"📂 {category}分类暂无预设\n\n💡 使用 /添加预设 {category} <名称> <内容> 来添加{category}预设")
            return

        result_text = f"📂 {category}预设列表\n\n"

        # 简洁的名称列表显示
        preset_names = list(category_presets.keys())
        result_text += f"({', '.join(preset_names)})\n\n"

        result_text += f"💡 共 {len(category_presets)} 个{category}预设\n"
        result_text += f"• /查询预设 <名称> - 查看完整内容\n"
        result_text += f"• /全部预设列表 - 查看所有分类"

        # 检查是否使用伪造转发
        if self._should_use_fake_forward_presets():
            forward_chain = self._create_fake_forward_message(f"{category}预设列表", result_text, f"{category}预设管理")
            yield event.chain_result(forward_chain)
        else:
            yield event.plain_result(result_text)

    @filter.command("添加预设分类")
    async def add_preset_category(self, event):
        '''添加新的预设分类'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "添加预设分类"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/添加预设分类"):
                category_name = message_text[7:].strip()
            elif message_text.startswith("添加预设分类"):
                category_name = message_text[6:].strip()
            else:
                category_name = message_text

            if not category_name:
                yield event.plain_result("❌ 用法: /添加预设分类 <分类名>\n例如: /添加预设分类 表情")
                return

            if category_name in self.preset_categories:
                yield event.plain_result(f"❌ 分类 '{category_name}' 已存在\n\n📂 现有分类: {', '.join(self.preset_categories.keys())}")
                return

            # 添加新分类
            self.preset_categories[category_name] = []
            self.save_preset_categories()

            yield event.plain_result(f"✅ 预设分类 '{category_name}' 添加成功！\n\n📂 现有分类: {', '.join(self.preset_categories.keys())}")

        except Exception as e:
            logger.error(f"添加预设分类失败: {e}")
            yield event.plain_result(f"❌ 添加预设分类失败: {str(e)}")

    @filter.command("预设分类列表")
    async def list_preset_categories(self, event):
        '''显示所有预设分类'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "预设分类列表"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        if not self.preset_categories:
            yield event.plain_result("📂 当前没有任何预设分类")
            return

        result_text = "📂 预设分类列表\n\n"

        for category, presets in self.preset_categories.items():
            result_text += f"• **{category}** ({len(presets)}个预设)\n"

        result_text += f"\n💡 总计: {len(self.preset_categories)} 个分类\n"
        result_text += "• /添加预设分类 <名称> - 添加新分类\n"
        result_text += "• /<分类名>预设列表 - 查看分类预设"

        yield event.plain_result(result_text)

    @filter.command("开启预设转发")
    async def enable_preset_forward(self, event):
        '''开启预设列表的伪造转发显示'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "开启预设转发"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        self.enable_fake_forward_presets = True
        self.config['enable_fake_forward_presets'] = True
        self.config.save_config()

        yield event.plain_result("✅ 预设列表伪造转发已开启")

    @filter.command("关闭预设转发")
    async def disable_preset_forward(self, event):
        '''关闭预设列表的伪造转发显示'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "关闭预设转发"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        self.enable_fake_forward_presets = False
        self.config['enable_fake_forward_presets'] = False
        self.config.save_config()

        yield event.plain_result("✅ 预设列表伪造转发已关闭\n\n💡 现在预设列表将以普通消息形式显示")

    @filter.command("nai参数")
    async def extract_nai_metadata(self, event):
        '''从NovelAI生成的图片中提取生成参数'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "nai参数"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        try:
            # 获取图片URL
            image_urls = []
            current_images = self.extract_images_from_messages(event.message_obj.message)
            image_urls.extend(current_images)

            if not image_urls:
                reply_images = self.get_reply_message_images(event)
                image_urls.extend(reply_images)

            if not image_urls:
                yield event.plain_result("❌ 未检测到图片\n\n📖 用法: /nai参数 + 图片\n💡 支持直接发送图片或回复图片消息")
                return

            # 下载第一张图片
            image_url = image_urls[0]
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.get(image_url)
                    if response.status_code != 200:
                        yield event.plain_result(f"❌ 下载图片失败: HTTP {response.status_code}")
                        return
                    image_data = response.content

                    # 检查图片大小
                    if len(image_data) > Constants.MAX_IMAGE_SIZE:
                        yield event.plain_result(f"❌ 图片过大 ({len(image_data) / 1024 / 1024:.1f}MB)，超过限制 ({Constants.MAX_IMAGE_SIZE / 1024 / 1024:.1f}MB)")
                        return

            except Exception as e:
                yield event.plain_result(f"❌ 下载图片失败: {str(e)}")
                return

            # 验证图片格式
            try:
                img = PILImage.open(io.BytesIO(image_data))

                # 检查图片格式
                if img.format != 'PNG':
                    yield event.plain_result("❌ 只支持PNG格式的图片\n\n💡 NovelAI生成的图片通常为PNG格式")
                    return

                # 检查图片尺寸
                width, height = img.size
                if max(width, height) > Constants.MAX_IMAGE_DIMENSION:
                    yield event.plain_result(f"❌ 图片尺寸过大 ({width}x{height})，超过限制 ({Constants.MAX_IMAGE_DIMENSION}px)")
                    return

                # 确保图片有Alpha通道
                if img.mode != 'RGBA':
                    yield event.plain_result("❌ 该图片不包含参数")
                    return

            except Exception as e:
                yield event.plain_result(f"❌ 图片格式错误: {str(e)}")
                return

            # 提取元数据
            try:
                metadata = self._extract_novelai_metadata(img)
                if not metadata:
                    yield event.plain_result("❌ 该图片不包含参数")
                    return

                # 格式化元数据显示
                result_text = self._format_novelai_metadata(metadata)

                # 使用伪造转发显示
                forward_chain = self._create_fake_forward_message("NovelAI图片参数", result_text, "参数分析器")
                yield event.chain_result(forward_chain)

            except (MetadataExtractionError, ImageProcessingError) as e:
                # 处理已知的验证错误
                yield event.plain_result("❌ 该图片不包含参数")
            except Exception as e:
                logger.error(f"提取NovelAI元数据失败: {e}")
                yield event.plain_result("❌ 该图片不包含参数")

        except Exception as e:
            logger.error(f"看nai参数命令失败: {e}")
            yield event.plain_result(f"❌ 命令执行失败: {str(e)}")

    def _extract_novelai_metadata(self, image: PILImage.Image) -> dict:
        """提取NovelAI图片的元数据"""

        # 转换为numpy数组
        image_array = np.array(image.convert("RGBA"))

        # 检查图片格式
        if image_array.shape[-1] != 4 or len(image_array.shape) != 3:
            raise MetadataExtractionError("图片格式不正确，需要RGBA格式")

        # 提取LSB数据
        alpha = image_array[..., -1]  # 获取Alpha通道
        alpha = alpha.T.reshape((-1,))
        alpha = alpha[:(alpha.shape[0] // 8) * 8]
        alpha = np.bitwise_and(alpha, 1)
        alpha = alpha.reshape((-1, 8))
        alpha = np.packbits(alpha, axis=1)

        # 创建数据读取器
        data = alpha
        pos = 0

        def get_next_n_bytes(n):
            nonlocal pos
            n_bytes = data[pos:pos + n]
            pos += n
            return bytearray(n_bytes)

        def read_32bit_integer():
            bytes_list = get_next_n_bytes(4)
            if len(bytes_list) == 4:
                return int.from_bytes(bytes_list, byteorder='big')
            return None

        # 验证魔数
        magic = "stealth_pngcomp"
        read_magic = get_next_n_bytes(len(magic)).decode("utf-8")
        if magic != read_magic:
            raise MetadataExtractionError("不是有效的NovelAI图片，缺少stealth pnginfo标识")

        # 读取JSON数据长度
        read_len = read_32bit_integer() // 8
        if read_len <= 0:
            raise MetadataExtractionError("无效的元数据长度")

        # 读取并解压JSON数据
        json_data = get_next_n_bytes(read_len)
        json_data = json.loads(gzip.decompress(json_data).decode("utf-8"))

        # 处理Comment字段
        if "Comment" in json_data and isinstance(json_data["Comment"], str):
            json_data["Comment"] = json.loads(json_data["Comment"])

        return json_data

    def _format_novelai_metadata(self, metadata: dict) -> str:
        """格式化NovelAI元数据为可读文本"""
        result_lines = []

        # 基本信息
        if "Software" in metadata:
            result_lines.append(f"🎨 生成软件: {metadata['Software']}")

        if "Source" in metadata:
            result_lines.append(f"🌐 来源: {metadata['Source']}")

        # 处理Comment字段（包含主要参数）
        if "Comment" in metadata and isinstance(metadata["Comment"], dict):
            comment = metadata["Comment"]

            # 提示词
            if "prompt" in comment:
                result_lines.append(f"\n📝 正面提示词:")
                result_lines.append(f"{comment['prompt']}")

            # 负面提示词
            if "uc" in comment:
                result_lines.append(f"\n🚫 负面提示词:")
                result_lines.append(f"{comment['uc']}")

            # 生成参数
            result_lines.append(f"\n⚙️ 生成参数:")

            if "steps" in comment:
                result_lines.append(f"• 步数: {comment['steps']}")

            if "scale" in comment:
                result_lines.append(f"• 引导强度: {comment['scale']}")

            if "cfg_rescale" in comment:
                result_lines.append(f"• CFG重缩放: {comment['cfg_rescale']}")

            if "seed" in comment:
                result_lines.append(f"• 种子: {comment['seed']}")

            if "sampler" in comment:
                result_lines.append(f"• 采样器: {comment['sampler']}")

            if "noise_schedule" in comment:
                result_lines.append(f"• 噪声调度: {comment['noise_schedule']}")

            # 图片尺寸
            if "width" in comment and "height" in comment:
                result_lines.append(f"• 尺寸: {comment['width']}x{comment['height']}")

            # 模型信息
            if "model" in comment:
                result_lines.append(f"• 模型: {comment['model']}")

            # 图生图参数
            if "strength" in comment:
                result_lines.append(f"• 图生图强度: {comment['strength']}")

            if "noise" in comment:
                result_lines.append(f"• 图生图噪声: {comment['noise']}")

            # 其他参数
            if "sm" in comment:
                result_lines.append(f"• SM: {comment['sm']}")

            if "sm_dyn" in comment:
                result_lines.append(f"• SM动态: {comment['sm_dyn']}")

            # Vibe Transfer信息
            if "reference_information_extracted" in comment:
                result_lines.append(f"• 参考信息提取: {comment['reference_information_extracted']}")

            if "reference_strength" in comment:
                result_lines.append(f"• 参考强度: {comment['reference_strength']}")

        # 其他元数据
        other_fields = ["Title", "Description", "Author", "Copyright"]
        for field in other_fields:
            if field in metadata and metadata[field]:
                result_lines.append(f"\n📋 {field}: {metadata[field]}")

        if not result_lines:
            return "❌ 未找到有效的生成参数"

        return "\n".join(result_lines)





    @filter.command("翻译")
    async def translate_command(self, event):
        '''翻译文本'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        try:
            # 从消息中提取要翻译的文本
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/翻译"):
                text_to_translate = message_text[3:].strip()
            elif message_text.startswith("翻译"):
                text_to_translate = message_text[2:].strip()
            else:
                text_to_translate = message_text

            if not text_to_translate:
                yield event.plain_result("❌ 用法: /翻译 <要翻译的文本>\n例如: /翻译 可爱的女孩")
                return

            if not self.baidu_translate_appid or not self.baidu_translate_secret:
                yield event.plain_result("❌ 百度翻译API未配置\n请在配置文件中设置 baidu_translate_appid 和 baidu_translate_secret")
                return

            # 检测语言并翻译
            if self.detect_chinese(text_to_translate):
                # 中文翻译为英文
                translated = await self.translate_text(text_to_translate, 'zh', 'en')
                if translated:
                    yield event.plain_result(f"🌐 翻译结果\n\n📝 原文: {text_to_translate}\n🔤 译文: {translated}")
                else:
                    yield event.plain_result("❌ 翻译失败，请稍后重试")
            else:
                # 英文翻译为中文
                translated = await self.translate_text(text_to_translate, 'en', 'zh')
                if translated:
                    yield event.plain_result(f"🌐 翻译结果\n\n📝 原文: {text_to_translate}\n🔤 译文: {translated}")
                else:
                    yield event.plain_result("❌ 翻译失败，请稍后重试")

        except Exception as e:
            logger.error(f"翻译命令失败: {e}")
            yield event.plain_result(f"❌ 翻译失败: {str(e)}")

    @filter.command("开启自动翻译")
    async def enable_auto_translate(self, event):
        '''开启画图时的自动翻译功能'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "开启自动翻译"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        if not self.baidu_translate_appid or not self.baidu_translate_secret:
            yield event.plain_result("❌ 百度翻译API未配置\n请在配置文件中设置 baidu_translate_appid 和 baidu_translate_secret")
            return

        self.enable_auto_translate = True
        self.config['enable_auto_translate'] = True
        self.config.save_config()

        yield event.plain_result("✅ 自动翻译已开启\n\n💡 现在画图时会自动将中文提示词翻译为英文")

    @filter.command("关闭自动翻译")
    async def disable_auto_translate(self, event):
        '''关闭画图时的自动翻译功能'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "关闭自动翻译"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        self.enable_auto_translate = False
        self.config['enable_auto_translate'] = False
        self.config.save_config()

        yield event.plain_result("✅ 自动翻译已关闭\n\n💡 现在画图时不会自动翻译中文提示词")

    @filter.command("图生图强度")
    async def set_img2img_strength(self, event):
        """设置图生图强度参数"""
        if not self._check_admin_permission(event, "图生图强度"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        # 提取参数
        message_text = str(event.get_message()).strip()
        parts = message_text.split()

        if len(parts) < 2:
            yield event.plain_result(f"❌ 请指定强度值\n\n📖 用法: /图生图强度 <0.1-1.0>\n💡 当前值: {self.img2img_strength}")
            return

        try:
            strength = float(parts[1])
            if not (0.1 <= strength <= 1.0):
                yield event.plain_result("❌ 强度值必须在0.1-1.0之间")
                return

            # 更新配置
            self.img2img_strength = strength
            self.config['img2img_strength'] = strength
            self.config.save_config()

            yield event.plain_result(f"✅ 图生图强度已设置为: {strength}\n\n💡 强度越高，生成图片与原图差异越大")

        except ValueError:
            yield event.plain_result("❌ 请输入有效的数值")

    @filter.command("图生图噪声")
    async def set_img2img_noise(self, event):
        """设置图生图噪声参数"""
        if not self._check_admin_permission(event, "图生图噪声"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        # 提取参数
        message_text = str(event.get_message()).strip()
        parts = message_text.split()

        if len(parts) < 2:
            yield event.plain_result(f"❌ 请指定噪声值\n\n📖 用法: /图生图噪声 <0.0-1.0>\n💡 当前值: {self.img2img_noise}")
            return

        try:
            noise = float(parts[1])
            if not (0.0 <= noise <= 1.0):
                yield event.plain_result("❌ 噪声值必须在0.0-1.0之间")
                return

            # 更新配置
            self.img2img_noise = noise
            self.config['img2img_noise'] = noise
            self.config.save_config()

            yield event.plain_result(f"✅ 图生图噪声已设置为: {noise}\n\n💡 噪声越高，生成图片随机性越大")

        except ValueError:
            yield event.plain_result("❌ 请输入有效的数值")

    @filter.command("图生图参数")
    async def show_img2img_params(self, event):
        """显示当前图生图参数"""
        result_text = f"⚙️ 图生图参数设置\n\n"
        result_text += f"🎚️ 强度 (Strength): {self.img2img_strength}\n"
        result_text += f"   • 范围: 0.1 - 1.0\n"
        result_text += f"   • 说明: 控制生成图片与原图的相似度\n"
        result_text += f"   • 效果: 值越高差异越大\n\n"

        result_text += f"🌊 噪声 (Noise): {self.img2img_noise}\n"
        result_text += f"   • 范围: 0.0 - 1.0\n"
        result_text += f"   • 说明: 控制生成过程的随机性\n"
        result_text += f"   • 效果: 值越高随机性越大\n\n"

        result_text += f"📝 修改命令:\n"
        result_text += f"• /图生图强度 <值>\n"
        result_text += f"• /图生图噪声 <值>\n"
        result_text += f"• 临时参数: -s<强度> -n<噪声> (如: -s0.6-n0.2)\n\n"

        result_text += f"💡 推荐设置:\n"
        result_text += f"• 轻微修改: 强度0.3-0.5, 噪声0.1-0.2\n"
        result_text += f"• 中等修改: 强度0.5-0.7, 噪声0.2-0.3\n"
        result_text += f"• 大幅修改: 强度0.7-0.9, 噪声0.3-0.5"

        # 检查是否使用伪造转发
        if self._should_use_fake_forward_presets():
            forward_chain = self._create_fake_forward_message("图生图参数设置", result_text, "参数管理器")
            yield event.chain_result(forward_chain)
        else:
            yield event.plain_result(result_text)


    @filter.command("开启图片审核")
    async def enable_image_audit(self, event):
        '''开启图片审核功能'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "开启图片审核"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        if not self.baidu_audit_api_key or not self.baidu_audit_secret_key:
            yield event.plain_result("❌ 百度审核API未配置\n请在后台管理面板中设置 baidu_audit_api_key 和 baidu_audit_secret_key")
            return

        self.enable_image_audit = True
        self.config['enable_image_audit'] = True
        self.config.save_config()

        # 创建审核失败图片保存目录
        Utils.create_directory_if_not_exists(self.audit_failed_save_path)

        yield event.plain_result(f"✅ 图片审核已开启\n\n💡 审核不通过的图片将保存到: {self.audit_failed_save_path}\n但不会发送给用户")

    @filter.command("关闭图片审核")
    async def disable_image_audit(self, event):
        '''关闭图片审核功能'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "关闭图片审核"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        self.enable_image_audit = False
        self.config['enable_image_audit'] = False
        self.config.save_config()

        yield event.plain_result("✅ 图片审核已关闭\n\n💡 现在所有生成的图片都会直接发送给用户")



    @filter.command("添加画风")
    async def add_style_preset(self, event):
        '''添加新的预设画风'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "添加画风"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/添加画风"):
                args_text = message_text[5:].strip()
            elif message_text.startswith("添加画风"):
                args_text = message_text[4:].strip()
            else:
                args_text = message_text
            
            # 分割参数
            parts = args_text.split(None, 1)
            
            if len(parts) < 2:
                yield event.plain_result("❌ 用法: /添加画风 <画风名> <画风描述>\n例如: /添加画风 科幻风 sci-fi, futuristic, high-tech, metallic")
                return
            
            style_name = parts[0]
            style_prompt = parts[1]
            
            if not style_name or not style_prompt:
                yield event.plain_result("❌ 画风名和描述不能为空")
                return
            
            # 检查画风名是否已存在
            if style_name in self.style_presets:
                # 单次回复，只显示原内容
                result_message = (
                    f"⚠️ 画风 '{style_name}' 已存在，画风将被覆盖\n"
                    f"原内容: {self.style_presets[style_name]}"
                )
            else:
                # 新画风
                result_message = f"✅ 画风 '{style_name}' 添加成功！\n内容: {style_prompt}"
            
            # 添加或更新画风
            self.style_presets[style_name] = style_prompt
            # 保存画风到配置文件
            self.save_style_presets()
            
            yield event.plain_result(result_message)
            
        except Exception as e:
            logger.error(f"添加画风失败: {e}")
            yield event.plain_result(f"❌ 添加画风失败: {str(e)}")

    @filter.command("删除画风")
    async def delete_style_preset(self, event):
        '''删除指定的预设画风'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "删除画风"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/删除画风"):
                style_name = message_text[5:].strip()
            elif message_text.startswith("删除画风"):
                style_name = message_text[4:].strip()
            else:
                style_name = message_text
            
            if not style_name:
                yield event.plain_result("❌ 用法: /删除画风 <画风名>")
                return
            
            if style_name not in self.style_presets:
                yield event.plain_result(f"❌ 画风 '{style_name}' 不存在\n\n可用画风: {', '.join(self.style_presets.keys())}")
                return
            
            deleted_content = self.style_presets.pop(style_name)
            # 重要：保存更新后的画风到配置文件
            self.save_style_presets()
            
            # 如果删除的是当前选中的画风，清空选择
            if self.selected_style_preset == style_name:
                self.config['selected_style_preset'] = ''
                self.selected_style_preset = ''
                self.config.save_config()
            
            yield event.plain_result(f"✅ 画风 '{style_name}' 删除成功！\n已删除内容: {deleted_content[:100]}{'...' if len(deleted_content) > 100 else ''}")
            
        except Exception as e:
            logger.error(f"删除画风失败: {e}")
            yield event.plain_result(f"❌ 删除画风失败: {str(e)}")

    @filter.command("画风列表")
    async def list_style_presets(self, event):
        '''显示所有可用的预设画风'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "画风列表"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        if not self.style_presets:
            yield event.plain_result("🎨 当前没有任何画风\n\n💡 使用 /添加画风 <名称> <描述> 来添加画风")
            return
        
        # 简化显示，只显示名称
        style_names = list(self.style_presets.keys())
        
        # 标记当前选中的画风
        if self.enable_style_preset and self.selected_style_preset in style_names:
            style_names_display = []
            for name in style_names:
                if name == self.selected_style_preset:
                    style_names_display.append(f"{name}✅")
                else:
                    style_names_display.append(name)
            style_list = f"🎨 画风列表: {', '.join(style_names_display)}\n\n"
        else:
            style_list = f"🎨 画风列表: {', '.join(style_names)}\n\n"
        
        status_text = ""
        if self.enable_style_preset:
            if self.selected_style_preset:
                status_text = f"🎯 当前全局画风: {self.selected_style_preset}\n"
            else:
                status_text = "⚠️ 画风功能已启用但未选择画风\n"
        else:
            status_text = "❌ 画风功能未启用\n"
        
        style_list += status_text
        style_list += "\n💡 画风使用方法:\n• 在提示词中包含画风名可临时使用\n• 例如: /画图 风景 油画风\n• /查询画风 <画风名> - 查看详细内容\n\n🔧 使用 /切换画风 <名称> 来更改全局画风"
        
        yield event.plain_result(style_list)

    @filter.command("切换画风")
    async def switch_style_preset(self, event):
        '''切换当前使用的画风'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "切换画风"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/切换画风"):
                style_name = message_text[5:].strip()
            elif message_text.startswith("切换画风"):
                style_name = message_text[4:].strip()
            else:
                style_name = message_text
            
            if not style_name:
                # 显示可用画风列表
                if not self.style_presets:
                    yield event.plain_result("❌ 当前没有任何画风\n\n💡 使用 /添加画风 <名称> <描述> 来添加画风")
                    return
                
                style_list = "❌ 用法: /切换画风 <画风名>\n\n🎨 可用画风:\n"
                for name in self.style_presets.keys():
                    current = " (当前)" if (self.enable_style_preset and self.selected_style_preset == name) else ""
                    style_list += f"  • {name}{current}\n"
                yield event.plain_result(style_list)
                return
            
            if style_name not in self.style_presets:
                yield event.plain_result(f"❌ 画风 '{style_name}' 不存在\n\n可用画风: {', '.join(self.style_presets.keys())}")
                return
            
            # 更新配置
            self.config['selected_style_preset'] = style_name
            self.selected_style_preset = style_name
            self.config.save_config()
            
            # 如果画风功能未启用，自动启用
            if not self.enable_style_preset:
                self.config['enable_style_preset'] = True
                self.enable_style_preset = True
                self.config.save_config()
                yield event.plain_result(f"✅ 已切换至画风 '{style_name}' 并自动启用画风功能\n内容: {self.style_presets[style_name][:100]}{'...' if len(self.style_presets[style_name]) > 100 else ''}")
            else:
                yield event.plain_result(f"✅ 已切换至画风 '{style_name}'\n内容: {self.style_presets[style_name][:100]}{'...' if len(self.style_presets[style_name]) > 100 else ''}")
            
        except Exception as e:
            logger.error(f"切换画风失败: {e}")
            yield event.plain_result(f"❌ 切换画风失败: {str(e)}")

    @filter.command("启用画风")
    async def enable_style(self, event):
        '''启用画风功能'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "启用画风"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            self.config['enable_style_preset'] = True
            self.enable_style_preset = True
            self.config.save_config()
            
            if self.selected_style_preset:
                yield event.plain_result(f"✅ 画风功能已启用\n当前画风: {self.selected_style_preset}")
            else:
                yield event.plain_result("✅ 画风功能已启用\n\n💡 使用 /切换画风 <名称> 来选择画风")
            
        except Exception as e:
            logger.error(f"启用画风失败: {e}")
            yield event.plain_result(f"❌ 启用画风失败: {str(e)}")

    @filter.command("关闭画风")
    async def disable_style(self, event):
        '''关闭画风功能'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "关闭画风"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            self.config['enable_style_preset'] = False
            self.enable_style_preset = False
            self.config.save_config()
            
            yield event.plain_result("✅ 画风功能已关闭\n\n💡 画风仍可通过在提示词中包含画风名来临时使用")
            
        except Exception as e:
            logger.error(f"关闭画风失败: {e}")
            yield event.plain_result(f"❌ 关闭画风失败: {str(e)}")

    @filter.command("添加负面")
    async def add_negative_prompt(self, event):
        '''添加新的负面提示词预设'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "添加负面"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/添加负面"):
                args_text = message_text[5:].strip()
            elif message_text.startswith("添加负面"):
                args_text = message_text[4:].strip()
            else:
                args_text = message_text
            
            # 分割参数
            parts = args_text.split(None, 1)
            
            if len(parts) < 2:
                yield event.plain_result("❌ 用法: /添加负面 <名称> <负面提示词>\n例如: /添加负面 高质量 lowres, bad anatomy, bad hands, worst quality")
                return
            
            negative_name = parts[0]
            negative_prompt = parts[1]
            
            if not negative_name or not negative_prompt:
                yield event.plain_result("❌ 名称和负面提示词不能为空")
                return
            
            # 检查名称是否已存在
            if negative_name in self.negative_prompts:
                # 单次回复，只显示原内容
                result_message = (
                    f"⚠️ 负面提示词 '{negative_name}' 已存在，负面提示词将被覆盖\n"
                    f"原内容: {self.negative_prompts[negative_name]}"
                )
            else:
                # 新负面提示词
                result_message = f"✅ 负面提示词 '{negative_name}' 添加成功！\n内容: {negative_prompt}"
            
            # 添加或更新负面提示词
            self.negative_prompts[negative_name] = negative_prompt
            # 保存负面提示词到配置文件
            self.save_negative_prompts()
            
            yield event.plain_result(result_message)
            
        except Exception as e:
            logger.error(f"添加负面提示词失败: {e}")
            yield event.plain_result(f"❌ 添加负面提示词失败: {str(e)}")

    @filter.command("删除负面")
    async def delete_negative_prompt(self, event):
        '''删除指定的负面提示词预设'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "删除负面"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/删除负面"):
                negative_name = message_text[5:].strip()
            elif message_text.startswith("删除负面"):
                negative_name = message_text[4:].strip()
            else:
                negative_name = message_text
            
            if not negative_name:
                yield event.plain_result("❌ 用法: /删除负面 <名称>")
                return
            
            if negative_name not in self.negative_prompts:
                yield event.plain_result(f"❌ 负面提示词 '{negative_name}' 不存在\n\n可用负面提示词: {', '.join(self.negative_prompts.keys())}")
                return
            
            deleted_content = self.negative_prompts.pop(negative_name)
            # 重要：保存更新后的负面提示词到配置文件
            self.save_negative_prompts()
            
            # 如果删除的是当前选中的负面提示词，清空选择
            if self.selected_negative_prompt == negative_name:
                self.config['selected_negative_prompt'] = ''
                self.selected_negative_prompt = ''
                self.config.save_config()
            
            yield event.plain_result(f"✅ 负面提示词 '{negative_name}' 删除成功！\n已删除内容: {deleted_content[:100]}{'...' if len(deleted_content) > 100 else ''}")
            
        except Exception as e:
            logger.error(f"删除负面提示词失败: {e}")
            yield event.plain_result(f"❌ 删除负面提示词失败: {str(e)}")

    @filter.command("负面列表")
    async def list_negative_prompts(self, event):
        '''显示所有可用的负面提示词预设'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "负面列表"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        if not self.negative_prompts:
            yield event.plain_result("🚫 当前没有任何负面提示词\n\n💡 使用 /添加负面 <名称> <负面提示词> 来添加")
            return
        
        # 简化显示，只显示名称
        negative_names = list(self.negative_prompts.keys())
        
        # 标记当前选中的负面提示词
        if self.enable_negative_prompt and self.selected_negative_prompt in negative_names:
            negative_names_display = []
            for name in negative_names:
                if name == self.selected_negative_prompt:
                    negative_names_display.append(f"{name}✅")
                else:
                    negative_names_display.append(name)
            negative_list = f"🚫 负面提示词列表: {', '.join(negative_names_display)}\n\n"
        else:
            negative_list = f"🚫 负面提示词列表: {', '.join(negative_names)}\n\n"
        
        status_text = ""
        if self.enable_negative_prompt:
            if self.selected_negative_prompt:
                status_text = f"🎯 当前负面提示词: {self.selected_negative_prompt}\n"
            else:
                status_text = "⚠️ 负面提示词功能已启用但未选择预设\n"
        else:
            status_text = "❌ 负面提示词功能未启用\n"
        
        negative_list += status_text
        negative_list += "\n💡 负面提示词作用:\n• 避免生成不想要的内容\n• 提高图片整体质量\n• /查询负面 <名称> - 查看详细内容\n\n🔧 使用 /切换负面 <名称> 来更改负面提示词"
        
        yield event.plain_result(negative_list)

    @filter.command("切换负面")
    async def switch_negative_prompt(self, event):
        '''切换当前使用的负面提示词'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "切换负面"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            # 从消息中提取参数
            message_text = event.message_str.strip()
            # 移除命令部分
            if message_text.startswith("/切换负面"):
                negative_name = message_text[5:].strip()
            elif message_text.startswith("切换负面"):
                negative_name = message_text[4:].strip()
            else:
                negative_name = message_text
            
            if not negative_name:
                # 显示可用负面提示词列表
                if not self.negative_prompts:
                    yield event.plain_result("❌ 当前没有任何负面提示词\n\n💡 使用 /添加负面 <名称> <负面提示词> 来添加")
                    return
                
                negative_list = "❌ 用法: /切换负面 <名称>\n\n🚫 可用负面提示词:\n"
                for name in self.negative_prompts.keys():
                    current = " (当前)" if (self.enable_negative_prompt and self.selected_negative_prompt == name) else ""
                    negative_list += f"  • {name}{current}\n"
                yield event.plain_result(negative_list)
                return
            
            if negative_name not in self.negative_prompts:
                yield event.plain_result(f"❌ 负面提示词 '{negative_name}' 不存在\n\n可用负面提示词: {', '.join(self.negative_prompts.keys())}")
                return
            
            # 更新配置
            self.config['selected_negative_prompt'] = negative_name
            self.selected_negative_prompt = negative_name
            self.config.save_config()
            
            # 如果负面提示词功能未启用，自动启用
            if not self.enable_negative_prompt:
                self.config['enable_negative_prompt'] = True
                self.enable_negative_prompt = True
                self.config.save_config()
                yield event.plain_result(f"✅ 已切换至负面提示词 '{negative_name}' 并自动启用功能\n内容: {self.negative_prompts[negative_name][:100]}{'...' if len(self.negative_prompts[negative_name]) > 100 else ''}")
            else:
                yield event.plain_result(f"✅ 已切换至负面提示词 '{negative_name}'\n内容: {self.negative_prompts[negative_name][:100]}{'...' if len(self.negative_prompts[negative_name]) > 100 else ''}")
            
        except Exception as e:
            logger.error(f"切换负面提示词失败: {e}")
            yield event.plain_result(f"❌ 切换负面提示词失败: {str(e)}")

    @filter.command("启用负面")
    async def enable_negative(self, event):
        '''启用负面提示词功能'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "启用负面"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            self.config['enable_negative_prompt'] = True
            self.enable_negative_prompt = True
            self.config.save_config()
            
            if self.selected_negative_prompt:
                yield event.plain_result(f"✅ 负面提示词功能已启用\n当前负面提示词: {self.selected_negative_prompt}")
            else:
                yield event.plain_result("✅ 负面提示词功能已启用\n\n💡 使用 /切换负面 <名称> 来选择负面提示词")
            
        except Exception as e:
            logger.error(f"启用负面提示词失败: {e}")
            yield event.plain_result(f"❌ 启用负面提示词失败: {str(e)}")

    @filter.command("关闭负面")
    async def disable_negative(self, event):
        '''关闭负面提示词功能'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "关闭负面"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        try:
            self.config['enable_negative_prompt'] = False
            self.enable_negative_prompt = False
            self.config.save_config()
            
            yield event.plain_result("✅ 负面提示词功能已关闭")
            
        except Exception as e:
            logger.error(f"关闭负面提示词失败: {e}")
            yield event.plain_result(f"❌ 关闭负面提示词失败: {str(e)}")

    @filter.command("图生图")
    async def image_to_image(self, event):
        '''使用NovelAI进行图生图（需要先发送图片）'''
        # 检查白名单
        if not self._check_whitelist(event):
            return
        
        # 检查管理员权限
        if not self._check_admin_permission(event, "图生图"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return
            
        async for result in self._handle_image_to_image(event):
            yield result

    @filter.command("画图配置")
    async def show_config(self, event):
        '''显示NovelAI插件配置信息'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "画图配置"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        model_info = self.default_model if self.default_model else "未配置"
        model_version = ""
        if self.default_model:
            model_version = " (v4/v4.5)" if self.is_v4_model(self.default_model) else " (v3)"

        # 画风状态
        style_status = "未启用"
        if self.enable_style_preset:
            style_status = f"已启用 - {self.selected_style_preset}" if self.selected_style_preset else "已启用但未选择画风"

        # 负面提示词状态
        negative_status = "未启用"
        if self.enable_negative_prompt:
            negative_status = f"已启用 - {self.selected_negative_prompt}" if self.selected_negative_prompt else "已启用但未选择预设"

        # 翻译功能状态
        translate_status = "未配置"
        if self.baidu_translate_appid and self.baidu_translate_secret:
            translate_status = "已配置"
            if self.enable_auto_translate:
                translate_status += " | 自动翻译已启用"
            else:
                translate_status += " | 自动翻译未启用"

        # 图片审核功能状态
        audit_status = "未配置"
        if self.baidu_audit_api_key and self.baidu_audit_secret_key:
            audit_status = "已配置"
            if self.enable_image_audit:
                # 严格程度描述
                strictness = {1: "严格", 2: "中等", 3: "宽松"}.get(self.audit_conclusion_type, "未知")

                # 审核场景
                scenes = []
                if self.audit_enable_politics: scenes.append("政治")
                if self.audit_enable_porn: scenes.append("色情")
                if self.audit_enable_terror: scenes.append("暴恐")
                if self.audit_enable_disgust: scenes.append("恶心")
                scenes_text = "+".join(scenes) if scenes else "无"

                audit_status += f" | 图片审核已启用({strictness}模式, {scenes_text})"
            else:
                audit_status += " | 图片审核未启用"

        config_text = (
            f"⚙️ NovelAI插件配置\n\n"
            f"🤖 模型: {model_info}{model_version}\n"
            f"📐 分辨率: {self.default_resolution}\n"
            f"⚙️ 参数: {self.default_steps}步 | 权重{self.default_scale} | {self.default_sampler}\n"
            f"🎨 画风: {style_status}\n"
            f"🚫 负面提示词: {negative_status}\n"
            f"🌐 翻译功能: {translate_status}\n"
            f"🔍 图片审核: {audit_status}\n"
            f"💾 自动保存: {'启用' if self.enable_auto_save else '未启用'}\n"
            f"📊 显示参数: {'启用' if self.show_params else '关闭'}\n"
            f"📋 已加载预设: {len(self.presets)} 个\n"
            f"🖌️ 已加载画风: {len(self.style_presets)} 个\n"
            f"🚫 已加载负面提示词: {len(self.negative_prompts)} 个"
        )

        if self.whitelist_groups:
            config_text += f"\n\n🔒 群组限制: 仅限 {len(self.whitelist_groups)} 个群组"

        # 检查是否使用伪造转发
        if self._should_use_fake_forward_presets():
            forward_chain = self._create_fake_forward_message("NovelAI插件配置信息", config_text, "配置管理器")
            yield event.chain_result(forward_chain)
        else:
            yield event.plain_result(config_text)

    @filter.command("衣服分类")
    async def show_clothing_categories(self, event):
        '''显示衣服分类信息'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        try:
            # 检查是否指定了具体分类
            message_text = event.message_str.strip()
            if message_text.startswith("/衣服分类"):
                args_text = message_text[5:].strip()
            elif message_text.startswith("衣服分类"):
                args_text = message_text[4:].strip()
            else:
                args_text = ""

            if args_text:
                # 显示指定分类的详细信息
                category_info = self.clothing_manager.format_clothing_list(args_text, 10)
                if self.enable_fake_forward_presets:
                    forward_chain = self._create_fake_forward_message(f"{args_text}分类详情", category_info, "衣服管理器")
                    yield event.chain_result(forward_chain)
                else:
                    yield event.plain_result(category_info)
            else:
                # 显示所有分类概览
                categories = self.clothing_manager.get_categories()
                category_info = []
                category_info.append("🎀 衣服分类列表")
                category_info.append("")

                for category in categories:
                    count = self.clothing_manager.get_category_count(category)
                    range_info = self.clothing_manager.get_category_range(category)
                    category_info.append(f"{category}: {count}套 ({range_info})")

                category_info.append("")
                category_info.append("💡 使用方法:")
                category_info.append("• /衣服分类 职业装 - 查看职业装详情")
                category_info.append("• /画图 美少女 随机衣服 - 随机选择衣服")
                category_info.append("• /画图 美少女 001 - 使用指定编号衣服")

                result_text = "\n".join(category_info)

                if self.enable_fake_forward_presets:
                    forward_chain = self._create_fake_forward_message("衣服分类信息", result_text, "衣服管理器")
                    yield event.chain_result(forward_chain)
                else:
                    yield event.plain_result(result_text)

        except Exception as e:
            logger.error(f"显示衣服分类失败: {e}")
            yield event.plain_result(f"❌ 显示衣服分类失败: {str(e)}")

    @filter.command("衣服统计")
    async def show_clothing_statistics(self, event):
        '''显示衣服数据库统计信息'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        try:
            stats_text = self.clothing_manager.get_statistics()

            if self.enable_fake_forward_presets:
                forward_chain = self._create_fake_forward_message("衣服数据库统计", stats_text, "衣服管理器")
                yield event.chain_result(forward_chain)
            else:
                yield event.plain_result(stats_text)

        except Exception as e:
            logger.error(f"显示衣服统计失败: {e}")
            yield event.plain_result(f"❌ 显示衣服统计失败: {str(e)}")

    @filter.command("衣服搜索")
    async def search_clothing(self, event):
        '''搜索衣服标签'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        try:
            # 提取搜索关键词
            message_text = event.message_str.strip()
            if message_text.startswith("/衣服搜索"):
                keyword = message_text[5:].strip()
            elif message_text.startswith("衣服搜索"):
                keyword = message_text[4:].strip()
            else:
                keyword = message_text

            if not keyword:
                yield event.plain_result("❌ 请提供搜索关键词\n用法: /衣服搜索 关键词")
                return

            results = self.clothing_manager.search_clothing(keyword)

            if not results:
                yield event.plain_result(f"🔍 未找到包含 '{keyword}' 的衣服")
                return

            # 限制显示结果数量
            max_results = 10
            search_info = []
            search_info.append(f"🔍 搜索结果: '{keyword}' (共{len(results)}个)")
            search_info.append("")

            for i, (number, category, name, tags) in enumerate(results[:max_results]):
                search_info.append(f"{number}: {category} - {name}")
                # 截断过长的标签
                short_tags = tags[:60] + "..." if len(tags) > 60 else tags
                search_info.append(f"     {short_tags}")
                search_info.append("")

            if len(results) > max_results:
                search_info.append(f"... 还有 {len(results) - max_results} 个结果")

            result_text = "\n".join(search_info)

            if self.enable_fake_forward_presets:
                forward_chain = self._create_fake_forward_message(f"衣服搜索: {keyword}", result_text, "衣服管理器")
                yield event.chain_result(forward_chain)
            else:
                yield event.plain_result(result_text)

        except Exception as e:
            logger.error(f"搜索衣服失败: {e}")
            yield event.plain_result(f"❌ 搜索衣服失败: {str(e)}")

    @filter.command("衣服重载")
    async def reload_clothing_database(self, event):
        '''重新加载衣服数据库'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "衣服重载"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        try:
            self.clothing_manager.reload_database()
            yield event.plain_result("✅ 衣服数据库重载完成")
        except Exception as e:
            logger.error(f"重载衣服数据库失败: {e}")
            yield event.plain_result(f"❌ 重载衣服数据库失败: {str(e)}")



    @filter.command("画图统计")
    async def show_statistics(self, event):
        '''显示NovelAI插件全局使用统计'''
        # 检查白名单
        if not self._check_whitelist(event):
            return

        # 检查管理员权限
        if not self._check_admin_permission(event, "画图统计"):
            yield event.plain_result("❌ 仅管理员可使用此命令")
            return

        # 获取全局统计信息
        global_stats = self.usage_manager.get_global_statistics()

        # 当前队列信息
        queue_info = ""
        if global_stats['processing_request']:
            queue_info = f"\n🎨 正在处理: 1个请求"
        if global_stats['queue_length'] > 0:
            queue_info += f"\n🕒 排队等待: {global_stats['queue_length']}个请求"

        # 构建最近7天的使用趋势
        recent_trend = ""
        if global_stats['recent_days']:
            recent_trend = "\n\n📈 最近7天使用趋势:\n"
            for day_data in global_stats['recent_days'][:5]:  # 只显示最近5天
                date_str = day_data['date'][-5:]  # 只显示月-日
                recent_trend += f"  {date_str}: {day_data['count']}次\n"

        stats_text = (
            f"📊 NovelAI使用统计\n\n"
            f"📅 今日总使用: {global_stats['today_total']}次\n"
            f"📊 历史总使用: {global_stats['total_usage']}次\n"
            f"🔥 今日活跃: {global_stats['active_users_today']}人"
            f"{queue_info}"
            f"{recent_trend}"
        )

        # 检查是否使用伪造转发
        if self._should_use_fake_forward_presets():
            forward_chain = self._create_fake_forward_message("NovelAI使用统计", stats_text, "统计管理器")
            yield event.chain_result(forward_chain)
        else:
            yield event.plain_result(stats_text)

    # ========== API调用方法 ==========
    async def _call_novelai_txt2img(self, prompt: str, width: int = 832, height: int = 1216, 
                                    steps: int = 28, scale: int = 7, sampler: str = "k_euler") -> tuple:
        """
        调用NovelAI文生图API
        返回: (图片数据, 元数据) 或 (None, 错误信息)
        """
        try:
            # 参数验证
            if not self.api_token:
                raise NovelAIError.ConfigurationError("未配置API Token")
            
            if not self.default_model:
                raise NovelAIError.ConfigurationError("未配置模型名称")
                
            if not prompt or not prompt.strip():
                raise NovelAIError.ParameterError("提示词不能为空")

            if steps < 1 or steps > 50:
                raise NovelAIError.ParameterError(f"生成步数 {steps} 超出有效范围 (1-50)")

            if scale < 1 or scale > 15:
                raise NovelAIError.ParameterError(f"提示词引导权重 {scale} 超出有效范围 (1-15)")
            
            # 创建HTTP客户端
            try:
                async with httpx.AsyncClient(timeout=60.0) as client:
                    headers = {
                        "Authorization": f"Bearer {self.api_token}",
                        "Content-Type": "application/json",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                    }
                    
                    parameters = self.get_model_parameters(
                        self.default_model, prompt, width, height, steps, scale, sampler
                    )
                    
                    payload = {
                        "input": prompt,
                        "model": self.default_model,
                        "action": "generate",
                        "parameters": parameters
                    }
                    
                    api_url = self.get_api_url("generate-image")
                    logger.info(f"发送NovelAI请求到: {api_url}")
                    logger.info(f"模型: {self.default_model} ({'v4/v4.5' if self.is_v4_model(self.default_model) else 'v3'}), 采样器: {sampler}")
                    logger.info(f"实际发送的Prompt长度: {len(prompt)} 字符")
                    
                    # 发送请求
                    try:
                        response = await client.post(api_url, headers=headers, json=payload)
                        
                        logger.info(f"响应状态: {response.status_code}")
                        
                        # 处理不同的状态码
                        if response.status_code == 200:
                            # 成功
                            image_data = response.content
                            logger.info(f"成功获取响应数据，大小: {len(image_data)} bytes")
                            return image_data, {"success": True}
                        elif response.status_code == 401:
                            # 认证失败
                            raise NovelAIError.AuthenticationError(response.status_code, response.text)
                        elif response.status_code == 429:
                            # 超出配额
                            raise NovelAIError.QuotaExceededError(response.status_code, response.text)
                        elif response.status_code >= 500:
                            # 服务器错误
                            raise NovelAIError.ServerError(response.status_code, response.text)
                        else:
                            # 其他API错误
                            raise NovelAIError.APIError(response.status_code, response.text)
                    except httpx.RequestError as e:
                        # 请求错误（网络问题）
                        raise NovelAIError.NetworkError(f"请求失败: {str(e)}", e)
            except httpx.TimeoutException as e:
                # 超时错误
                raise NovelAIError.NetworkError(f"请求超时 (60秒)", e)
                    
        except NovelAIError.NetworkError as e:
            logger.error(f"网络错误: {e}")
            return None, {"error": str(e), "error_type": "network"}
        except NovelAIError.AuthenticationError as e:
            logger.error(f"认证错误: {e}")
            return None, {"error": str(e), "error_type": "auth"}
        except NovelAIError.QuotaExceededError as e:
            logger.error(f"配额超限: {e}")
            return None, {"error": str(e), "error_type": "quota"}
        except NovelAIError.ServerError as e:
            logger.error(f"服务器错误: {e}")
            return None, {"error": str(e), "error_type": "server"}
        except NovelAIError.APIError as e:
            logger.error(f"API错误: {e}")
            return None, {"error": str(e), "error_type": "api"}
        except NovelAIError.ConfigurationError as e:
            logger.error(f"配置错误: {e}")
            return None, {"error": str(e), "error_type": "config"}
        except NovelAIError.ParameterError as e:
            logger.error(f"参数错误: {e}")
            return None, {"error": str(e), "error_type": "param"}
        except Exception as e:
            logger.error(f"未知错误: {e}")
            return None, {"error": f"未知错误: {str(e)}", "error_type": "unknown"}

    async def _call_novelai_img2img(self, prompt: str, reference_image: str,
                                    width: int = 832, height: int = 1216,
                                    steps: int = 28, scale: int = 7,
                                    strength: float = 0.7, noise: float = 0.2) -> tuple:
        """
        调用NovelAI图生图API
        返回: (图片数据, 元数据) 或 (None, 错误信息)
        """
        try:
            # 参数验证
            if not self.api_token:
                raise NovelAIError.ConfigurationError("未配置API Token")
            
            if not self.default_model:
                raise NovelAIError.ConfigurationError("未配置模型名称")
                
            if not reference_image:
                raise NovelAIError.ParameterError("参考图片不能为空")

            if steps < 1 or steps > 50:
                raise NovelAIError.ParameterError(f"生成步数 {steps} 超出有效范围 (1-50)")

            if scale < 1 or scale > 15:
                raise NovelAIError.ParameterError(f"提示词引导权重 {scale} 超出有效范围 (1-15)")

            if strength < 0.1 or strength > 0.9:
                raise NovelAIError.ParameterError(f"图生图强度 {strength} 超出有效范围 (0.1-0.9)")

            if noise < 0.0 or noise > 1.0:
                raise NovelAIError.ParameterError(f"图生图噪声 {noise} 超出有效范围 (0.0-1.0)")

            # 创建HTTP客户端
            try:
                async with httpx.AsyncClient(timeout=60.0) as client:
                    headers = {
                        "Authorization": f"Bearer {self.api_token}",
                        "Content-Type": "application/json",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                    }
                    
                    # 获取基础参数
                    parameters = self.get_model_parameters(
                        self.default_model, prompt, width, height, steps, scale, self.default_sampler
                    )
                    
                    # 添加图生图特有的参数
                    parameters.update({
                        "image": reference_image,
                        "strength": strength,
                        "noise": noise
                    })
                    
                    payload = {
                        "input": prompt,
                        "model": self.default_model,
                        "action": "img2img",
                        "parameters": parameters
                    }
                    
                    api_url = self.get_api_url("generate-image")
                    logger.info(f"发送图生图请求，模型: {self.default_model}, action: img2img, 强度: {strength}, 噪声: {noise}")
                    
                    # 发送请求
                    try:
                        response = await client.post(api_url, headers=headers, json=payload)
                        
                        logger.info(f"图生图响应状态: {response.status_code}")
                        
                        # 处理不同的状态码
                        if response.status_code == 200:
                            # 成功
                            return response.content, {"success": True}
                        elif response.status_code == 401:
                            # 认证失败
                            raise NovelAIError.AuthenticationError(response.status_code, response.text)
                        elif response.status_code == 429:
                            # 超出配额
                            raise NovelAIError.QuotaExceededError(response.status_code, response.text)
                        elif response.status_code >= 500:
                            # 服务器错误 - 记录详细信息
                            error_details = {
                                "status_code": response.status_code,
                                "response_text": response.text,
                                "request_params": {
                                    "model": self.default_model,
                                    "action": "img2img",
                                    "strength": strength,
                                    "noise": noise,
                                    "prompt_length": len(prompt),
                                    "steps": steps,
                                    "scale": scale,
                                    "width": width,
                                    "height": height
                                }
                            }
                            logger.error(f"图生图服务器错误详情: {error_details}")
                            raise NovelAIError.ServerError(response.status_code, response.text)
                        else:
                            # 其他API错误
                            raise NovelAIError.APIError(response.status_code, response.text)
                    except httpx.RequestError as e:
                        # 请求错误（网络问题）
                        raise NovelAIError.NetworkError(f"请求失败: {str(e)}", e)
            except httpx.TimeoutException as e:
                # 超时错误
                raise NovelAIError.NetworkError(f"请求超时 (60秒)", e)
                    
        except NovelAIError.NetworkError as e:
            logger.error(f"图生图网络错误: {e}")
            return None, {"error": str(e), "error_type": "network"}
        except NovelAIError.AuthenticationError as e:
            logger.error(f"图生图认证错误: {e}")
            return None, {"error": str(e), "error_type": "auth"}
        except NovelAIError.QuotaExceededError as e:
            logger.error(f"图生图配额超限: {e}")
            return None, {"error": str(e), "error_type": "quota"}
        except NovelAIError.ServerError as e:
            logger.error(f"图生图服务器错误: {e}")
            return None, {"error": str(e), "error_type": "server"}
        except NovelAIError.APIError as e:
            logger.error(f"图生图API错误: {e}")
            return None, {"error": str(e), "error_type": "api"}
        except NovelAIError.ConfigurationError as e:
            logger.error(f"图生图配置错误: {e}")
            return None, {"error": str(e), "error_type": "config"}
        except NovelAIError.ParameterError as e:
            logger.error(f"图生图参数错误: {e}")
            return None, {"error": str(e), "error_type": "param"}
        except Exception as e:
            logger.error(f"图生图未知错误: {e}")
            return None, {"error": f"未知错误: {str(e)}", "error_type": "unknown"}
